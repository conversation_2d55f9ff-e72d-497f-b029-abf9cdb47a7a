<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lysjk.mapper.OperateLogMapper">

  <!-- 操作日志结果映射 -->
  <resultMap id="BaseResultMap" type="com.lysjk.entity.OperateLog">
    <id column="oid" jdbcType="INTEGER" property="oid" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="operation_url" jdbcType="VARCHAR" property="operationUrl" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="cost_time" jdbcType="INTEGER" property="costTime" />
  </resultMap>

  <!-- 基础字段列表 -->
  <sql id="Base_Column_List">
    oid, user_id, username, operation_name, operation_type, operation_url, operate_time, cost_time
  </sql>

  <!-- 批量删除操作日志 -->
  <delete id="deleteBatch">
    DELETE FROM operate_log
    WHERE oid IN
    <foreach collection="list" item="id" open="(" separator="," close=")">
      #{id,jdbcType=INTEGER}
    </foreach>
  </delete>

  <!-- 分页条件查询操作日志 -->
  <select id="pageQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM operate_log
    <where>
      <if test="username != null and username != ''">
        AND username::varchar LIKE CONCAT('%', #{username,jdbcType=VARCHAR}::varchar, '%')
      </if>
      <if test="operationName != null and operationName != ''">
        AND operation_name::varchar LIKE CONCAT('%', #{operationName,jdbcType=VARCHAR}::varchar, '%')
      </if>
      <if test="operationType != null and operationType != ''">
        AND operation_type::varchar LIKE CONCAT('%', #{operationType,jdbcType=VARCHAR}::varchar, '%')
      </if>
      <if test="operationUrl != null and operationUrl != ''">
        AND operation_url::varchar LIKE CONCAT('%', #{operationUrl,jdbcType=VARCHAR}::varchar, '%')
      </if>
      <if test="userId != null">
        AND user_id = #{userId,jdbcType=INTEGER}
      </if>
      <if test="startTime != null">
        AND operate_time &gt;= #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        AND operate_time &lt;= #{endTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    ORDER BY operate_time DESC
  </select>

</mapper>
