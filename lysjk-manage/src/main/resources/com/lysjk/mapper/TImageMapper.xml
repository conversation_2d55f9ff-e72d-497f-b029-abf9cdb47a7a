<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lysjk.mapper.TImageMapper">

  <!-- 基础结果映射 -->
  <!--    typeHandler="com.lysjk.config.typehandler.JsonStringTypeHandler"-->
  <resultMap id="BaseResultMap" type="com.lysjk.entity.TImage">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="acquisition_dt" jdbcType="TIMESTAMP" property="acquisitionDt" />
    <result column="sensor_type" jdbcType="VARCHAR" property="sensorType" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="meta_data" jdbcType="OTHER" property="metaData"/>
    <result column="create_dt" jdbcType="TIMESTAMP" property="createDt" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="update_dt" jdbcType="TIMESTAMP" property="updateDt" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="region_id" jdbcType="INTEGER" property="regionId" />
    <result column="bounds" jdbcType="OTHER" property="bounds" typeHandler="com.lysjk.config.typehandler.PolygonTypeHandler" />
  </resultMap>

  <!-- 基础字段列表 -->
  <sql id="Base_Column_List">
    id, "name", acquisition_dt, sensor_type, file_path, meta_data::text as meta_data,
    create_dt, create_by, update_dt, update_by, remark, region_id, bounds
  </sql>

  <!-- 业务字段列表（排除系统字段） -->
  <sql id="Business_Column_List">
    "name", acquisition_dt, sensor_type, file_path, meta_data::text as meta_data, remark, region_id, bounds
  </sql>

  <!-- 根据主键查询 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_image"
    where id = #{id,jdbcType=INTEGER}
  </select>

  <!-- 查询所有影像信息（用于分页） -->
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_image"
    order by id desc
  </select>

  <!-- 根据地物ID查询影像信息列表 -->
  <select id="selectByRegionId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_image"
    where region_id = #{regionId,jdbcType=INTEGER}
    order by create_dt desc
  </select>

  <!-- 根据传感器类型查询影像信息列表 -->
  <select id="selectBySensorType" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_image"
    where sensor_type = #{sensorType,jdbcType=VARCHAR}
    order by create_dt desc
  </select>

  <!-- 根据主键删除 -->
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from "t_image"
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <!-- 批量删除影像信息 -->
  <delete id="deleteByIds">
    delete from "t_image" where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <!-- 新增影像信息 -->
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.lysjk.entity.TImage" useGeneratedKeys="true">
    insert into "t_image" ("name", acquisition_dt, sensor_type,
      file_path, meta_data, create_dt, create_by,
      update_dt, update_by, remark, region_id, bounds)
    values (#{name,jdbcType=VARCHAR}, #{acquisitionDt,jdbcType=TIMESTAMP}, #{sensorType,jdbcType=VARCHAR},
      #{filePath,jdbcType=VARCHAR}, #{metaData}::json, #{createDt,jdbcType=TIMESTAMP}, #{createBy,jdbcType=INTEGER},
      #{updateDt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR},
      #{regionId,jdbcType=INTEGER}, #{bounds,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PolygonTypeHandler})
  </insert>

  <!-- 选择性新增影像信息 -->
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lysjk.entity.TImage" useGeneratedKeys="true">
    insert into "t_image"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        "name",
      </if>
      <if test="acquisitionDt != null">
        acquisition_dt,
      </if>
      <if test="sensorType != null">
        sensor_type,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="metaData != null">
        meta_data,
      </if>
      <if test="createDt != null">
        create_dt,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateDt != null">
        update_dt,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="regionId != null">
        region_id,
      </if>
      <if test="bounds != null">
        bounds,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="acquisitionDt != null">
        #{acquisitionDt,jdbcType=TIMESTAMP},
      </if>
      <if test="sensorType != null">
        #{sensorType,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="metaData != null and metaData != ''">
        #{metaData}::json,
      </if>
      <if test="createDt != null">
        #{createDt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=INTEGER},
      </if>
      <if test="updateDt != null">
        #{updateDt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=INTEGER},
      </if>
      <if test="bounds != null">
        #{bounds,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PolygonTypeHandler},
      </if>
    </trim>
  </insert>

  <!-- 根据主键更新影像信息 -->
  <update id="updateByPrimaryKey" parameterType="com.lysjk.entity.TImage">
    update "t_image"
    set "name" = #{name,jdbcType=VARCHAR},
      acquisition_dt = #{acquisitionDt,jdbcType=TIMESTAMP},
      sensor_type = #{sensorType,jdbcType=VARCHAR},
      file_path = #{filePath,jdbcType=VARCHAR},
      meta_data = #{metaData}::json,
      create_dt = #{createDt,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=INTEGER},
      update_dt = #{updateDt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      region_id = #{regionId,jdbcType=INTEGER},
      bounds = #{bounds,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PolygonTypeHandler}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 更新业务字段（排除系统字段） -->
  <update id="updateBusinessFields" parameterType="com.lysjk.entity.TImage">
    update "t_image"
    <set>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="acquisitionDt != null">
        acquisition_dt = #{acquisitionDt,jdbcType=TIMESTAMP},
      </if>
      <if test="sensorType != null">
        sensor_type = #{sensorType,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="metaData != null and metaData != ''">
        meta_data = #{metaData}::json,
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        region_id = #{regionId,jdbcType=INTEGER},
      </if>
      <if test="bounds != null">
        bounds = #{bounds,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PolygonTypeHandler},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 分页条件查询影像信息 -->
  <select id="pageQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_image"
    <where>
      <if test="name != null and name != ''">
        and "name"::varchar like concat('%', #{name,jdbcType=VARCHAR}::varchar, '%')
      </if>
      <if test="sensorType != null and sensorType != ''">
        and sensor_type::varchar like concat('%', #{sensorType,jdbcType=VARCHAR}::varchar, '%')
      </if>
      <if test="regionId != null">
        and region_id = #{regionId,jdbcType=INTEGER}
      </if>
      <if test="acquisitionDt != null">
        and acquisition_dt = #{acquisitionDt,jdbcType=TIMESTAMP}
      </if>
      <if test="createBy != null">
        and create_by = #{createBy,jdbcType=INTEGER}
      </if>
      <if test="remark != null and remark != ''">
        and remark::varchar like concat('%', #{remark,jdbcType=VARCHAR}::varchar, '%')
      </if>
    </where>
    order by create_dt desc
  </select>

  <!-- 批量新增影像信息 -->
  <insert id="batchInsert">
    insert into "t_image" ("name", acquisition_dt, sensor_type, file_path, meta_data,
      create_dt, create_by, update_dt, update_by, remark, region_id, bounds)
    values
    <foreach collection="imageList" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.acquisitionDt,jdbcType=TIMESTAMP}, #{item.sensorType,jdbcType=VARCHAR},
       #{item.filePath,jdbcType=VARCHAR},
       <choose>
         <when test="item.metaData != null and item.metaData != ''">#{item.metaData}::json</when>
         <otherwise>null</otherwise>
       </choose>,
       #{item.createDt,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=INTEGER},
       #{item.updateDt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=INTEGER},
       #{item.remark,jdbcType=VARCHAR}, #{item.regionId,jdbcType=INTEGER},
       #{item.bounds,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PolygonTypeHandler})
    </foreach>
  </insert>

  <!-- 根据地物ID列表查询影像信息 -->
  <select id="selectByRegionIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_image"
    where region_id in
    <foreach collection="regionIds" item="regionId" open="(" close=")" separator=",">
      #{regionId}
    </foreach>
    order by create_dt desc
  </select>

</mapper>
