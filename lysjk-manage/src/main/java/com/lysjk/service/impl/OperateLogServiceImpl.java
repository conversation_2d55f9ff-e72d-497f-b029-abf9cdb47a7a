package com.lysjk.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lysjk.dto.OperateLogPageQueryDTO;
import com.lysjk.entity.OperateLog;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.OperateLogMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.OperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 操作日志服务实现类
 */
@Slf4j
@Service
public class OperateLogServiceImpl implements OperateLogService {

    @Autowired
    private OperateLogMapper operateLogMapper;

    /**
     * 批量删除操作日志
     * @param ids ID列表
     */
    @Override
    @Transactional
    public void deleteBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ValidationException.ParameterNullException("操作日志ID列表");
        }

        log.info("批量删除操作日志，ID列表: {}", ids);

        int deletedCount = operateLogMapper.deleteBatch(ids);

        if (deletedCount != ids.size()) {
            log.warn("批量删除操作日志部分失败，期望删除{}条，实际删除{}条", ids.size(), deletedCount);
        } else {
            log.info("批量删除操作日志成功，共删除{}条记录", deletedCount);
        }
    }

    /**
     * 分页条件查询操作日志
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult selectPage(OperateLogPageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页查询操作日志: page={}, pageSize={}, username={}, operationType={}, operationName={}, startTime={}, endTime={}",
                pageQueryDTO.getPage(), pageQueryDTO.getPageSize(),
                pageQueryDTO.getUsername(), pageQueryDTO.getOperationType(),
                pageQueryDTO.getOperationName(), pageQueryDTO.getStartTime(), pageQueryDTO.getEndTime());

        // 开启分页
        PageHelper.startPage(pageQueryDTO.getPage(), pageQueryDTO.getPageSize());

        // 执行查询
        Page<OperateLog> page = operateLogMapper.pageQuery(pageQueryDTO);

        // 封装结果
        long total = page.getTotal();
        List<OperateLog> result = page.getResult();

        log.info("分页查询完成，共查询到{}条记录", total);
        return new PageResult(total, result);
    }
}
