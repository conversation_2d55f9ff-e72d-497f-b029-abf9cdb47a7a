package com.lysjk.service.impl;

import com.lysjk.dto.excel.*;
import com.lysjk.entity.*;
import com.lysjk.exception.BusinessException;
import com.lysjk.exception.ValidationException;
import com.lysjk.service.*;
import com.lysjk.utils.CoordinateUtil;
import com.lysjk.utils.GeometryUtil;
import com.lysjk.vo.excel.TMonitoringPointExcelVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel解析服务实现类
 */
@Slf4j
@Service
public class ExcelParseServiceImpl implements ExcelParseService {

    @Autowired
    private TRegionInfoService regionInfoService;

    @Autowired
    private TMonitoringPointService monitoringPointService;

    @Autowired
    private TWaterEnvironmentService waterEnvironmentService;

    @Autowired
    private TWaterQualityService waterQualityService;

    @Autowired
    private TWaterSpectrumService waterSpectrumService;

    @Autowired
    private TMonitoringRecordService monitoringRecordService;

    @Autowired
    private TImageService imageService;

    // Excel工作表名称常量
    private static final String SHEET_REGION_INFO = "主表";
    private static final String SHEET_MONITORING_POINT = "监测点信息表";
    private static final String SHEET_WATER_ENVIRONMENT = "环境参数表";
    private static final String SHEET_WATER_QUALITY = "水质参数表";
    private static final String SHEET_WATER_SPECTRUM = "反射率光谱表";
    private static final String SHEET_MONITORING_RECORD = "测量记录表";
    private static final String SHEET_IMAGE = "影像信息表";

    // 拼接字符串
    private static final String date = " 00:00";

    @Override
    @Transactional
    public String parseAndSaveExcel(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ValidationException.ParameterNullException("Excel文件");
        }

        log.info("开始解析Excel文件: {}", file.getOriginalFilename());

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            StringBuilder result = new StringBuilder();
            
            // 第一步：录入地物信息主表
            List<TRegionInfoMainExcelDTO> regionInfoList = parseRegionInfoMainFromWorkbook(workbook);
            if (!regionInfoList.isEmpty()) {
                saveRegionInfoList(regionInfoList);
                result.append("成功录入地物信息 ").append(regionInfoList.size()).append(" 条；");
                log.info("成功录入地物信息 {} 条", regionInfoList.size());
            }

            // 第二步：录入监测点信息表
            List<TMonitoringPointExcelDTO> monitoringPointList = parseMonitoringPointFromWorkbook(workbook);
            if (!monitoringPointList.isEmpty()) {
                saveMonitoringPointList(monitoringPointList);
                result.append("成功录入监测点信息 ").append(monitoringPointList.size()).append(" 条；");
                log.info("成功录入监测点信息 {} 条", monitoringPointList.size());
            }

            // 第三步：录入其他表格数据
            // 水域环境表
            List<TWaterEnvironmentExcelDTO> waterEnvironmentList = parseWaterEnvironmentFromWorkbook(workbook);
            if (!waterEnvironmentList.isEmpty()) {
                saveWaterEnvironmentList(waterEnvironmentList);
                result.append("成功录入水域环境信息 ").append(waterEnvironmentList.size()).append(" 条；");
                log.info("成功录入水域环境信息 {} 条", waterEnvironmentList.size());
            }

            // 水质参数表
            List<TWaterQualityExcelDTO> waterQualityList = parseWaterQualityFromWorkbook(workbook);
            if (!waterQualityList.isEmpty()) {
                saveWaterQualityList(waterQualityList);
                result.append("成功录入水质参数信息 ").append(waterQualityList.size()).append(" 条；");
                log.info("成功录入水质参数信息 {} 条", waterQualityList.size());
            }

            // 反射率光谱表
            List<TWaterSpectrumExcelDTO> waterSpectrumList = parseWaterSpectrumFromWorkbook(workbook);
            if (!waterSpectrumList.isEmpty()) {
                saveWaterSpectrumList(waterSpectrumList);
                result.append("成功录入反射率光谱信息 ").append(waterSpectrumList.size()).append(" 条；");
                log.info("成功录入反射率光谱信息 {} 条", waterSpectrumList.size());
            }

            // 测量记录表
            List<TMonitoringRecordExcelDTO> monitoringRecordList = parseMonitoringRecordFromWorkbook(workbook);
            if (!monitoringRecordList.isEmpty()) {
                saveMonitoringRecordList(monitoringRecordList);
                result.append("成功录入测量记录信息 ").append(monitoringRecordList.size()).append(" 条；");
                log.info("成功录入测量记录信息 {} 条", monitoringRecordList.size());
            }

            // 影像信息表
            List<TImageExcelDTO> imageList = parseImageFromWorkbook(workbook);
            if (!imageList.isEmpty()) {
                saveImageList(imageList);
                result.append("成功录入影像信息 ").append(imageList.size()).append(" 条；");
                log.info("成功录入影像信息 {} 条", imageList.size());
            }

            String resultMessage = result.toString();
            if (resultMessage.isEmpty()) {
                resultMessage = "Excel文件解析完成，但未找到有效数据";
            }
            
            log.info("Excel文件解析完成: {}", resultMessage);
            return resultMessage;

        } catch (IOException e) {
            log.error("读取Excel文件失败: {}", file.getOriginalFilename(), e);
            throw new BusinessException.OperationFailedException("读取Excel文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("解析Excel文件失败: {}", file.getOriginalFilename(), e);
            throw new BusinessException.OperationFailedException("解析Excel文件失败: " + e.getMessage());
        }
    }

    @Override
    public List<TRegionInfoMainExcelDTO> parseRegionInfoMain(MultipartFile file) {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            return parseRegionInfoMainFromWorkbook(workbook);
        } catch (IOException e) {
            log.error("解析地物信息主表失败", e);
            throw new BusinessException.OperationFailedException("解析地物信息主表失败: " + e.getMessage());
        }
    }

    @Override
    public List<TMonitoringPointExcelDTO> parseMonitoringPoint(MultipartFile file) {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            return parseMonitoringPointFromWorkbook(workbook);
        } catch (IOException e) {
            log.error("解析监测点信息表失败", e);
            throw new BusinessException.OperationFailedException("解析监测点信息表失败: " + e.getMessage());
        }
    }

    @Override
    public List<TWaterEnvironmentExcelDTO> parseWaterEnvironment(MultipartFile file) {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            return parseWaterEnvironmentFromWorkbook(workbook);
        } catch (IOException e) {
            log.error("解析水域环境表失败", e);
            throw new BusinessException.OperationFailedException("解析水域环境表失败: " + e.getMessage());
        }
    }

    @Override
    public List<TWaterQualityExcelDTO> parseWaterQuality(MultipartFile file) {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            return parseWaterQualityFromWorkbook(workbook);
        } catch (IOException e) {
            log.error("解析水质参数表失败", e);
            throw new BusinessException.OperationFailedException("解析水质参数表失败: " + e.getMessage());
        }
    }

    @Override
    public List<TWaterSpectrumExcelDTO> parseWaterSpectrum(MultipartFile file) {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            return parseWaterSpectrumFromWorkbook(workbook);
        } catch (IOException e) {
            log.error("解析反射率光谱表失败", e);
            throw new BusinessException.OperationFailedException("解析反射率光谱表失败: " + e.getMessage());
        }
    }

    @Override
    public List<TMonitoringRecordExcelDTO> parseMonitoringRecord(MultipartFile file) {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            return parseMonitoringRecordFromWorkbook(workbook);
        } catch (IOException e) {
            log.error("解析测量记录表失败", e);
            throw new BusinessException.OperationFailedException("解析测量记录表失败: " + e.getMessage());
        }
    }

    @Override
    public List<TImageExcelDTO> parseImage(MultipartFile file) {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            return parseImageFromWorkbook(workbook);
        } catch (IOException e) {
            log.error("解析影像信息表失败", e);
            throw new BusinessException.OperationFailedException("解析影像信息表失败: " + e.getMessage());
        }
    }

    /**
     * 从工作簿解析地物信息主表
     */
    private List<TRegionInfoMainExcelDTO> parseRegionInfoMainFromWorkbook(Workbook workbook) {
        Sheet sheet = workbook.getSheet(SHEET_REGION_INFO);
        if (sheet == null) {
            log.warn("未找到工作表: {}", SHEET_REGION_INFO);
            return new ArrayList<>();
        }

        List<TRegionInfoMainExcelDTO> result = new ArrayList<>();
        
        // 从第2行开始读取数据（第1行是表头）
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null || isEmptyRow(row)) {
                continue;
            }

            try {
                TRegionInfoMainExcelDTO dto = parseRegionInfoMainRow(row);
                if (dto != null) {
                    result.add(dto);
                }
            } catch (Exception e) {
                log.error("解析地物信息主表第{}行失败", i + 1, e);
                throw new BusinessException.OperationFailedException("解析地物信息主表第" + (i + 1) + "行失败: " + e.getMessage());
            }
        }

        log.info("解析地物信息主表完成，共解析 {} 条数据", result.size());
        return result;
    }

    /**
     * 从工作簿解析监测点信息表
     */
    private List<TMonitoringPointExcelDTO> parseMonitoringPointFromWorkbook(Workbook workbook) {
        Sheet sheet = workbook.getSheet(SHEET_MONITORING_POINT);
        if (sheet == null) {
            log.warn("未找到工作表: {}", SHEET_MONITORING_POINT);
            return new ArrayList<>();
        }

        List<TMonitoringPointExcelDTO> result = new ArrayList<>();
        
        // 从第2行开始读取数据（第1行是表头）
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null || isEmptyRow(row)) {
                continue;
            }

            try {
                TMonitoringPointExcelDTO dto = parseMonitoringPointRow(row);
                if (dto != null) {
                    result.add(dto);
                }
            } catch (Exception e) {
                log.error("解析监测点信息表第{}行失败", i + 1, e);
                throw new BusinessException.OperationFailedException("解析监测点信息表第" + (i + 1) + "行失败: " + e.getMessage());
            }
        }

        log.info("解析监测点信息表完成，共解析 {} 条数据", result.size());
        return result;
    }

    /**
     * 从工作簿解析水域环境表
     */
    private List<TWaterEnvironmentExcelDTO> parseWaterEnvironmentFromWorkbook(Workbook workbook) {
        Sheet sheet = workbook.getSheet(SHEET_WATER_ENVIRONMENT);
        if (sheet == null) {
            log.warn("未找到工作表: {}", SHEET_WATER_ENVIRONMENT);
            return new ArrayList<>();
        }

        List<TWaterEnvironmentExcelDTO> result = new ArrayList<>();

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null || isEmptyRow(row)) {
                continue;
            }

            try {
                TWaterEnvironmentExcelDTO dto = parseWaterEnvironmentRow(row);
                if (dto != null) {
                    result.add(dto);
                }
            } catch (Exception e) {
                log.error("解析水域环境表第{}行失败", i + 1, e);
                throw new BusinessException.OperationFailedException("解析水域环境表第" + (i + 1) + "行失败: " + e.getMessage());
            }
        }

        log.info("解析水域环境表完成，共解析 {} 条数据", result.size());
        return result;
    }

    /**
     * 从工作簿解析水质参数表
     */
    private List<TWaterQualityExcelDTO> parseWaterQualityFromWorkbook(Workbook workbook) {
        Sheet sheet = workbook.getSheet(SHEET_WATER_QUALITY);
        if (sheet == null) {
            log.warn("未找到工作表: {}", SHEET_WATER_QUALITY);
            return new ArrayList<>();
        }

        List<TWaterQualityExcelDTO> result = new ArrayList<>();

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null || isEmptyRow(row)) {
                continue;
            }

            try {
                TWaterQualityExcelDTO dto = parseWaterQualityRow(row);
                if (dto != null) {
                    result.add(dto);
                }
            } catch (Exception e) {
                log.error("解析水质参数表第{}行失败", i + 1, e);
                throw new BusinessException.OperationFailedException("解析水质参数表第" + (i + 1) + "行失败: " + e.getMessage());
            }
        }

        log.info("解析水质参数表完成，共解析 {} 条数据", result.size());
        return result;
    }

    /**
     * 从工作簿解析反射率光谱表
     * 注意此时要保证excel规范,因为没有提供最大列
     */
    private List<TWaterSpectrumExcelDTO> parseWaterSpectrumFromWorkbook(Workbook workbook) {
        Sheet sheet = workbook.getSheet(SHEET_WATER_SPECTRUM);
        if (sheet == null) {
            log.warn("未找到工作表: {}", SHEET_WATER_SPECTRUM);
            return new ArrayList<>();
        }

        List<TWaterSpectrumExcelDTO> result = new ArrayList<>();
        String wavelength = null;
        String code = null;
        int columnNum = sheet.getRow(0).getLastCellNum();

        for(int j = 1; j<columnNum; j++) {
            code = getCellStringValue(sheet.getRow(1).getCell(j));
            TWaterSpectrumExcelDTO dto = new TWaterSpectrumExcelDTO();
            Map<String, Double> spectrum = new HashMap<>();
            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null || isEmptyRow(row)) {
                    continue;
                }

                try {
                    wavelength = getCellStringValue(row.getCell(0));
                    Cell cell = row.getCell(j);
                    if (cell != null) {
                        Double value = getCellDoubleValue(cell);
                        if (value != null && !value.isNaN() && !value.isInfinite()) {
                            spectrum.put(wavelength, value);
                        }
                    }

                } catch (Exception e) {
                    log.error("解析反射率光谱表第{}行失败", i + 1, e);
                    throw new BusinessException.OperationFailedException("解析反射率光谱表第" + (i + 1) + "行失败: " + e.getMessage());
                }
            }
            // 设置波长
            dto.setSpectrum(spectrum);
            // 采样点号
            dto.setCode(code);
            log.info("波普数据: {}", dto);
            if (dto != null) {
                result.add(dto);
            }
        }

        int i = 1/0;
        log.info("解析反射率光谱表完成，共解析 {} 条数据", result.size());
        return result;
    }

    /**
     * 从工作簿解析测量记录表
     */
    private List<TMonitoringRecordExcelDTO> parseMonitoringRecordFromWorkbook(Workbook workbook) {
        Sheet sheet = workbook.getSheet(SHEET_MONITORING_RECORD);
        if (sheet == null) {
            log.warn("未找到工作表: {}", SHEET_MONITORING_RECORD);
            return new ArrayList<>();
        }

        List<TMonitoringRecordExcelDTO> result = new ArrayList<>();

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null || isEmptyRow(row)) {
                continue;
            }

            try {
                TMonitoringRecordExcelDTO dto = parseMonitoringRecordRow(row);
                if (dto != null) {
                    result.add(dto);
                }
            } catch (Exception e) {
                log.error("解析测量记录表第{}行失败", i + 1, e);
                throw new BusinessException.OperationFailedException("解析测量记录表第" + (i + 1) + "行失败: " + e.getMessage());
            }
        }

        log.info("解析测量记录表完成，共解析 {} 条数据", result.size());
        return result;
    }

    /**
     * 从工作簿解析影像信息表
     */
    private List<TImageExcelDTO> parseImageFromWorkbook(Workbook workbook) {
        Sheet sheet = workbook.getSheet(SHEET_IMAGE);
        if (sheet == null) {
            log.warn("未找到工作表: {}", SHEET_IMAGE);
            return new ArrayList<>();
        }

        List<TImageExcelDTO> result = new ArrayList<>();

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null || isEmptyRow(row)) {
                continue;
            }

            try {
                TImageExcelDTO dto = parseImageRow(row);
                if (dto != null) {
                    result.add(dto);
                }
            } catch (Exception e) {
                log.error("解析影像信息表第{}行失败", i + 1, e);
                throw new BusinessException.OperationFailedException("解析影像信息表第" + (i + 1) + "行失败: " + e.getMessage());
            }
        }

        log.info("解析影像信息表完成，共解析 {} 条数据", result.size());
        return result;
    }

    /**
     * 解析地物信息主表行数据
     */
    private TRegionInfoMainExcelDTO parseRegionInfoMainRow(Row row) {
        TRegionInfoMainExcelDTO dto = new TRegionInfoMainExcelDTO();

        // 序号
        dto.setNumber(getCellStringValue(row.getCell(0)));

        // 地物编码
        dto.setCode(getCellStringValue(row.getCell(1)));

        // 采集时间（需要拼接00:00）
        String dateStr = getCellStringValue(row.getCell(2)) + date ;
        if (dateStr != null && !dateStr.trim().isEmpty()) {
            dto.setSampleDt(parseDateWithDefaultTime(dateStr));
            log.info("日期为: {}", dto.getSampleDt());
        }

        // 采集地点
        dto.setName(getCellStringValue(row.getCell(3)));

        // 采集方法
        dto.setMethod(getCellStringValue(row.getCell(4)));

        // 完成单位
        dto.setUnit(getCellStringValue(row.getCell(5)));

        // 备注
        dto.setRemark(getCellStringValue(row.getCell(6)));

        return dto;
    }

    /**
     * 解析监测点信息表行数据
     */
    private TMonitoringPointExcelDTO parseMonitoringPointRow(Row row) {
        TMonitoringPointExcelDTO dto = new TMonitoringPointExcelDTO();

        // 采样点号
        dto.setCode(getCellStringValue(row.getCell(1)));

        // 地点
        dto.setName(getCellStringValue(row.getCell(2)));

        // 经度和纬度（需要进行度分秒转换）
        String longitudeStr = getCellStringValue(row.getCell(3));
        String latitudeStr = getCellStringValue(row.getCell(4));

        if (longitudeStr != null && latitudeStr != null &&
            !longitudeStr.trim().isEmpty() && !latitudeStr.trim().isEmpty()) {
            try {
                double[] coordinates = CoordinateUtil.parseAndValidateCoordinates(longitudeStr, latitudeStr);
                Point location = GeometryUtil.createPoint(coordinates[0], coordinates[1]);
                dto.setLocation(location);
            } catch (Exception e) {
                log.error("解析坐标失败: 经度={}, 纬度={}", longitudeStr, latitudeStr, e);
                throw new IllegalArgumentException("坐标解析失败: " + e.getMessage());
            }
        }

        return dto;
    }

    /**
     * 解析水域环境表行数据
     */
    private TWaterEnvironmentExcelDTO parseWaterEnvironmentRow(Row row) {
        TWaterEnvironmentExcelDTO dto = new TWaterEnvironmentExcelDTO();

        // 采样点号
        dto.setCode(getCellStringValue(row.getCell(1)));

        // 监测时间（需要日期和时间拼接）
        String dateStr = getCellStringValue(row.getCell(3));
        String timeStr = getCellStringValue(row.getCell(4));
        if (dateStr != null && timeStr != null &&
            !dateStr.trim().isEmpty() && !timeStr.trim().isEmpty()) {
            dto.setMonitoringDt(parseDateTimeFromDateAndTime(dateStr, timeStr));
        }

        // 风速
        dto.setWindSpeed(getCellDoubleValue(row.getCell(7)));

        // 风向
        dto.setWindDirection(getCellStringValue(row.getCell(8)));

        // 海拔
        dto.setElevation(getCellDoubleValue(row.getCell(9)));

        // 气温
        dto.setAirTemperature(getCellDoubleValue(row.getCell(10)));

        // 水温
        dto.setWaterTemperature(getCellDoubleValue(row.getCell(11)));

        // pH
        dto.setPh(getCellDoubleValue(row.getCell(12)));

        // 溶解氧
        dto.setDissolvedOxyg(getCellDoubleValue(row.getCell(13)));

        // 水深度
        dto.setWaterDepth(getCellDoubleValue(row.getCell(14)));

        // 水面状况
        dto.setSurfaceCondition(getCellStringValue(row.getCell(15)));

        // 天气状况
        dto.setWeather(getCellStringValue(row.getCell(16)));

        // 照片序号
        dto.setPhotoFolder(getCellStringValue(row.getCell(17)));

        // 反射率光谱
        dto.setReflectanceSpectrum(getCellStringValue(row.getCell(18)));

        // 备注
        dto.setRemark(getCellStringValue(row.getCell(19)));

        log.info("水域环境表内容为: {}", dto);
        return dto;
    }

    /**
     * 解析水质参数表行数据
     */
    private TWaterQualityExcelDTO parseWaterQualityRow(Row row) {
        TWaterQualityExcelDTO dto = new TWaterQualityExcelDTO();

        // 采样点号
        dto.setCode(getCellStringValue(row.getCell(1)));

        // 透明度
        dto.setTransparency(getCellDoubleValue(row.getCell(2)));

        // 浊度
        dto.setTurbidity(getCellDoubleValue(row.getCell(3)));

        // 总悬浮物
        dto.setTss(getCellDoubleValue(row.getCell(4)));

        // 高锰酸盐指数
        dto.setPermanganateIndex(getCellDoubleValue(row.getCell(5)));

        // 总磷
        dto.setTp(getCellDoubleValue(row.getCell(6)));

        // 总氮
        dto.setTn(getCellDoubleValue(row.getCell(7)));

        // 氨氮
        dto.setAmmoniaNitrogen(getCellDoubleValue(row.getCell(8)));

        // 黄色物质
        dto.setYellowSubstance(getCellDoubleValue(row.getCell(9)));

        // 叶绿素a
        dto.setChlorophyll(getCellDoubleValue(row.getCell(10)));

        return dto;
    }

    /**
     * 解析测量记录表行数据
     */
    private TMonitoringRecordExcelDTO parseMonitoringRecordRow(Row row) {
        TMonitoringRecordExcelDTO dto = new TMonitoringRecordExcelDTO();

        // 采样点号
        dto.setCode(getCellStringValue(row.getCell(1)));

        // 点位测量记录
        dto.setPointMeasure(getCellStringValue(row.getCell(2)));

        // 风向风速
        dto.setWindDirectionSpeed(getCellStringValue(row.getCell(3)));

        // 光谱测量
        dto.setSpectralMeasure(getCellStringValue(row.getCell(4)));

        // 透明度测量
        dto.setTransparencyMeasure(getCellStringValue(row.getCell(5)));

        // 浊度测量
        dto.setTurbidityMeasure(getCellStringValue(row.getCell(6)));

        // 深度测量
        dto.setDepthMeasure(getCellStringValue(row.getCell(7)));

        // 水色照片
        dto.setWaterColorPhoto(getCellStringValue(row.getCell(8)));

        // 实验室检测
        dto.setLaboratoryTest(getCellStringValue(row.getCell(9)));

        return dto;
    }

    /**
     * 解析影像信息表行数据
     */
    private TImageExcelDTO parseImageRow(Row row) {
        TImageExcelDTO dto = new TImageExcelDTO();

        // 过境时间（需要拼接00:00）
        String dateStr = getCellStringValue(row.getCell(1)) + date;
        if (dateStr != null && !dateStr.trim().isEmpty()) {
            dto.setAcquisitionDt(parseDateWithDefaultTime(dateStr));
        }

        // 卫星传感器
        dto.setSensorType(getCellStringValue(row.getCell(2)));

        return dto;
    }

    /**
     * 工具方法：获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return DateTimeFormatter.ofPattern("yyyy-MM-dd").format(
                        cell.getLocalDateTimeCellValue().toLocalDate());
                } else {
                    // 如果是数字，转换为字符串（去掉小数点后的0）
                    double numValue = cell.getNumericCellValue();
                    if (numValue == (long) numValue) {
                        return String.valueOf((long) numValue);
                    } else {
                        return String.valueOf(numValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return null;
        }
    }

    /**
     * 工具方法：获取单元格数值
     */
    private Double getCellDoubleValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case STRING:
                String strValue = cell.getStringCellValue().trim();
                if (strValue.isEmpty()) {
                    return null;
                }
                try {
                    return Double.parseDouble(strValue);
                } catch (NumberFormatException e) {
                    log.warn("无法将字符串转换为数值: {}", strValue);
                    return null;
                }
            case FORMULA:
                try {
                    return cell.getNumericCellValue();
                } catch (Exception e) {
                    log.warn("无法获取公式单元格的数值");
                    return null;
                }
            default:
                return null;
        }
    }

    /**
     * 工具方法：解析日期并设置默认时间为00:00
     */
    private LocalDateTime parseDateWithDefaultTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试多种日期格式
            String[] patterns = {
                "yyyy-MM-dd HH:mm",
                "yyyy/MM/dd",
                "yyyy年MM月dd日",
                "MM/dd/yyyy",
                "dd/MM/yyyy"
            };

            for (String pattern : patterns) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                    LocalDate date = LocalDate.parse(dateStr.trim(), formatter);
                    return date.atStartOfDay(); // 设置时间为00:00:00
                } catch (Exception e) {
                    // 继续尝试下一个格式
                }
            }

            throw new IllegalArgumentException("无法解析日期格式: " + dateStr);
        } catch (Exception e) {
            log.error("解析日期失败: {}", dateStr, e);
            throw new IllegalArgumentException("日期解析失败: " + e.getMessage());
        }
    }

    /**
     * 工具方法：从日期和时间字符串解析为LocalDateTime
     */
    private LocalDateTime parseDateTimeFromDateAndTime(String dateStr, String timeStr) {
        if (dateStr == null || timeStr == null ||
            dateStr.trim().isEmpty() || timeStr.trim().isEmpty()) {
            return null;
        }

        try {
            LocalDate date = LocalDate.parse(dateStr.trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalTime time = LocalTime.parse(timeStr.trim(), DateTimeFormatter.ofPattern("HH:mm"));
            return LocalDateTime.of(date, time);
        } catch (Exception e) {
            log.error("解析日期时间失败: 日期={}, 时间={}", dateStr, timeStr, e);
            throw new IllegalArgumentException("日期时间解析失败: " + e.getMessage());
        }
    }

    /**
     * 工具方法：检查行是否为空
     */
    private boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }

        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String value = getCellStringValue(cell);
                if (value != null && !value.trim().isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 保存地物信息列表
     */
    private void saveRegionInfoList(List<TRegionInfoMainExcelDTO> list) {
        for (TRegionInfoMainExcelDTO dto : list) {
            TRegionInfo entity = new TRegionInfo();
            BeanUtils.copyProperties(dto, entity);
            regionInfoService.save(entity);
        }
    }

    /**
     * 保存监测点信息列表
     */
    private void saveMonitoringPointList(List<TMonitoringPointExcelDTO> list) {
        for (TMonitoringPointExcelDTO dto : list) {
            TMonitoringPoint entity = new TMonitoringPoint();
            BeanUtils.copyProperties(dto, entity);

            // 根据name查询地物信息表获取regionId
            if (dto.getName() != null && !dto.getName().trim().isEmpty()) {
                try {
                    TRegionInfo regionInfo = regionInfoService.selectByName(dto.getName().trim());
                    if (regionInfo != null) {
                        entity.setRegionId(regionInfo.getId());
                        log.debug("监测点{}关联到地物信息: regionId={}, regionName={}",
                            dto.getCode(), regionInfo.getId(), regionInfo.getName());
                    } else {
                        log.warn("未找到名称为{}的地物信息，监测点{}将不关联地物", dto.getName(), dto.getCode());
                    }
                } catch (Exception e) {
                    log.error("根据地点名称查询地物信息失败: {}", dto.getName(), e);
                }
            }
            monitoringPointService.save(entity);
        }
    }

    /**
     * 保存水域环境信息列表
     */
    private void saveWaterEnvironmentList(List<TWaterEnvironmentExcelDTO> list) {
        for (TWaterEnvironmentExcelDTO dto : list) {
            TWaterEnvironment entity = new TWaterEnvironment();
            BeanUtils.copyProperties(dto, entity);

            // 根据code查询监测点信息获取pointId和regionId
            if (dto.getCode() != null && !dto.getCode().trim().isEmpty()) {
                try {
                    TMonitoringPointExcelVO vo = monitoringPointService.selectExcelByCode(dto.getCode());
                    entity.setPointId(vo.getPointId());
                    entity.setRegionId(vo.getRegionId());
                } catch (Exception e) {
                    log.warn("根据采样点号查询监测点信息失败: {}", dto.getCode());
                }
            }

            waterEnvironmentService.save(entity);
        }
    }

    /**
     * 保存水质参数信息列表
     */
    private void saveWaterQualityList(List<TWaterQualityExcelDTO> list) {
        for (TWaterQualityExcelDTO dto : list) {
            TWaterQuality entity = new TWaterQuality();
            BeanUtils.copyProperties(dto, entity);

            // 根据code查询监测点信息获取pointId和regionId
            if (dto.getCode() != null && !dto.getCode().trim().isEmpty()) {
                try {
                    TMonitoringPointExcelVO vo = monitoringPointService.selectExcelByCode(dto.getCode());
                    entity.setPointId(vo.getPointId());
                    entity.setRegionId(vo.getRegionId());
                } catch (Exception e) {
                    log.warn("根据采样点号查询监测点信息失败: {}", dto.getCode());
                }
            }

            waterQualityService.save(entity);
        }
    }

    /**
     * 保存反射率光谱信息列表
     */
    private void saveWaterSpectrumList(List<TWaterSpectrumExcelDTO> list) {
        for (TWaterSpectrumExcelDTO dto : list) {
            TWaterSpectrum entity = new TWaterSpectrum();
            BeanUtils.copyProperties(dto, entity);

            // 根据code查询监测点信息获取pointId和regionId
            if (dto.getCode() != null && !dto.getCode().trim().isEmpty()) {
                try {
                    TMonitoringPointExcelVO vo = monitoringPointService.selectExcelByCode(dto.getCode());
                    entity.setPointId(vo.getPointId());
                    entity.setRegionId(vo.getRegionId());
                } catch (Exception e) {
                    log.warn("根据采样点号查询监测点信息失败: {}", dto.getCode());
                }
            }

            waterSpectrumService.save(entity);
        }
    }

    /**
     * 保存测量记录信息列表
     */
    private void saveMonitoringRecordList(List<TMonitoringRecordExcelDTO> list) {
        for (TMonitoringRecordExcelDTO dto : list) {
            TMonitoringRecord entity = new TMonitoringRecord();
            BeanUtils.copyProperties(dto, entity);

            // 根据code查询监测点信息获取pointId和regionId
            if (dto.getCode() != null && !dto.getCode().trim().isEmpty()) {
                try {
                    TMonitoringPointExcelVO vo = monitoringPointService.selectExcelByCode(dto.getCode());
                    entity.setPointId(vo.getPointId());
                    entity.setRegionId(vo.getRegionId());
                } catch (Exception e) {
                    log.warn("根据采样点号查询监测点信息失败: {}", dto.getCode());
                }
            }

            monitoringRecordService.save(entity);
        }
    }

    /**
     * 保存影像信息列表
     */
    private void saveImageList(List<TImageExcelDTO> list) {
        for (TImageExcelDTO dto : list) {
            TImage entity = new TImage();
            BeanUtils.copyProperties(dto, entity);
            imageService.save(entity);
        }
    }
}
