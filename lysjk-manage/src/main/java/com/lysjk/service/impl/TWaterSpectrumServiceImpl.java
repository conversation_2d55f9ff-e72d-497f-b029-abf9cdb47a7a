package com.lysjk.service.impl;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.TWaterSpectrumPageQueryDTO;
import com.lysjk.entity.TWaterSpectrum;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.BusinessException;
import com.lysjk.exception.FileOperationException;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.TWaterSpectrumMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.TWaterSpectrumService;
import com.lysjk.utils.DateTimeUtil;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 波谱信息服务实现类
 */
@Service
@Slf4j
public class TWaterSpectrumServiceImpl implements TWaterSpectrumService {

    @Autowired
    private TWaterSpectrumMapper waterSpectrumMapper;



    /**
     * 新增波谱信息
     * @param waterSpectrum 波谱信息
     */
    @Override
    public void save(TWaterSpectrum waterSpectrum) {
        if (waterSpectrum == null) {
            throw new ValidationException.ParameterNullException("波谱信息");
        }

        // 处理监测时间（如果未设置则使用当前时间）
        waterSpectrum.setMonitoringDt(DateTimeUtil.processMonitoringDateTime(waterSpectrum.getMonitoringDt()));

        log.info("新增波谱信息: pointId={}, regionId={}, monitoringDt={}",
                waterSpectrum.getPointId(), waterSpectrum.getRegionId(), waterSpectrum.getMonitoringDt());

        int result = waterSpectrumMapper.insert(waterSpectrum);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("新增波谱信息");
        }
    }

    /**
     * 批量删除波谱信息
     * @param ids ID列表
     */
    @Override
    @Transactional
    public void deleteBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ValidationException.ParameterNullException("删除ID列表");
        }

        // 权限验证
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if (!RoleConstant.ADMIN.equals(role)) {
            throw new AuthenticationException.InsufficientPermissionException("删除波谱信息");
        }

        log.info("批量删除波谱信息: {}", ids);

        int result = waterSpectrumMapper.deleteByIds(ids);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("删除波谱信息");
        }
    }

    /**
     * 更新波谱信息
     * @param waterSpectrum 波谱信息
     */
    @Override
    public void update(TWaterSpectrum waterSpectrum) {
        if (waterSpectrum == null) {
            throw new ValidationException.ParameterNullException("波谱信息");
        }

        if (waterSpectrum.getId() == null) {
            throw new ValidationException.ParameterNullException("波谱信息ID");
        }

        log.info("更新波谱信息: id={}, pointId={}, regionId={}",
                waterSpectrum.getId(), waterSpectrum.getPointId(), waterSpectrum.getRegionId());

        int result = waterSpectrumMapper.update(waterSpectrum);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("更新波谱信息");
        }
    }

    /**
     * 分页查询波谱信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult selectPage(TWaterSpectrumPageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页查询波谱信息: page={}, pageSize={}, pointId={}, regionId={}",
                pageQueryDTO.getPage(), pageQueryDTO.getPageSize(),
                pageQueryDTO.getPointId(), pageQueryDTO.getRegionId());

        // 开启分页
        PageHelper.startPage(pageQueryDTO.getPage(), pageQueryDTO.getPageSize());

        // 执行查询
        Page<TWaterSpectrum> page = waterSpectrumMapper.pageQuery(pageQueryDTO);

        // 封装结果
        long total = page.getTotal();
        List<TWaterSpectrum> result = page.getResult();

        log.info("分页查询完成，共查询到{}条记录", total);
        return new PageResult(total, result);
    }

    /**
     * 从CSV文件导入波谱数据
     * @param file CSV文件
     */
    @Override
    @Transactional
    public void importSpectrumFromCsv(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ValidationException.ParameterNullException("CSV文件");
        }

        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".csv")) {
            throw new ValidationException.ParameterFormatException("文件类型", "CSV格式");
        }

        log.info("开始处理CSV文件: {}, 大小: {} bytes", originalFilename, file.getSize());

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {

            List<TWaterSpectrum> spectrumList = parseCsvContent(reader);

            if (spectrumList.isEmpty()) {
                throw new ValidationException.ParameterFormatException("CSV文件", "文件中没有有效的波谱数据");
            }

            // 批量保存波谱数据
            int savedCount = 0;
            for (TWaterSpectrum spectrum : spectrumList) {
                try {
                    waterSpectrumMapper.insert(spectrum);
                    savedCount++;
                } catch (Exception e) {
                    log.error("保存波谱数据失败: {}", spectrum, e);
                    throw new BusinessException.OperationFailedException("保存波谱数据", e.getMessage());
                }
            }

            log.info("CSV导入完成，成功保存{}条波谱记录", savedCount);

        } catch (IOException e) {
            log.error("读取CSV文件失败: {}", originalFilename, e);
            throw new FileOperationException.FileUploadException("读取CSV文件失败: " + e.getMessage());
        }
    }
    /**
     * 解析CSV文件内容
     * @param reader CSV文件读取器
     * @return 波谱数据列表
     * @throws IOException 读取异常
     */
    private List<TWaterSpectrum> parseCsvContent(BufferedReader reader) throws IOException {
        List<TWaterSpectrum> spectrumList = new ArrayList<>();
        String line;
        int lineNumber = 0;
        String[] headers = null;

        while ((line = reader.readLine()) != null) {
            lineNumber++;
            line = line.trim();

            // 跳过空行
            if (line.isEmpty()) {
                continue;
            }

            String[] values = line.split(",");

            // 处理表头
            if (lineNumber == 1) {
                headers = values;
                log.debug("CSV表头: {}", Arrays.toString(headers));

                // 验证表头格式：第一列应该是波长，后续列是样本数据
                if (values.length < 2) {
                    throw new ValidationException.ParameterFormatException("CSV格式", "至少需要包含波长列和一个数据列");
                }
                continue;
            }

            // 处理数据行
            if (values.length < 2) {
                log.warn("第{}行数据格式不正确，跳过: {}", lineNumber, line);
                continue;
            }

            try {
                // 解析波长
                String wavelengthStr = values[0].trim();
                Double wavelength = Double.parseDouble(wavelengthStr);

                // 为每个样本列创建一个波谱记录
                for (int i = 1; i < values.length && i < headers.length; i++) {
                    String valueStr = values[i].trim();
                    if (valueStr.isEmpty()) {
                        continue;
                    }

                    Double spectrumValue = Double.parseDouble(valueStr);

                    // 查找或创建对应样本的波谱记录
                    String sampleName = headers[i].trim();
                    TWaterSpectrum spectrum = findOrCreateSpectrum(spectrumList, sampleName);

                    // 添加波长-数值对到spectrum的Map中
                    if (spectrum.getSpectrum() == null) {
                        spectrum.setSpectrum(new LinkedHashMap<>());
                    }
                    spectrum.getSpectrum().put(wavelengthStr, spectrumValue);
                }

            } catch (NumberFormatException e) {
                log.warn("第{}行数据解析失败，跳过: {}", lineNumber, line);
            }
        }

        log.info("CSV解析完成，共解析出{}个样本的波谱数据", spectrumList.size());
        return spectrumList;
    }
    /**
     * 查找或创建指定样本名称的波谱记录
     * @param spectrumList 波谱列表
     * @param sampleName 样本名称
     * @return 波谱记录
     */
    private TWaterSpectrum findOrCreateSpectrum(List<TWaterSpectrum> spectrumList, String sampleName) {
        // 查找是否已存在该样本的记录
        Optional<TWaterSpectrum> existingSpectrum = spectrumList.stream()
                .filter(spectrum -> sampleName.equals(spectrum.getRemark()))
                .findFirst();

        if (existingSpectrum.isPresent()) {
            return existingSpectrum.get();
        }

        // 创建新的波谱记录
        TWaterSpectrum newSpectrum = new TWaterSpectrum();
        newSpectrum.setMonitoringDt(LocalDateTime.now());
        newSpectrum.setRemark(sampleName); // 使用备注字段存储样本名称
        newSpectrum.setSpectrum(new LinkedHashMap<>());

        spectrumList.add(newSpectrum);
        log.debug("为样本 '{}' 创建新的波谱记录", sampleName);

        return newSpectrum;
    }

    /**
     * 批量新增波谱信息
     * @param waterSpectrumList 波谱信息列表
     */
    @Override
    public void batchInsert(List<TWaterSpectrum> waterSpectrumList) {
        if (waterSpectrumList == null || waterSpectrumList.isEmpty()) {
            throw new ValidationException.ParameterNullException("波谱信息列表");
        }

        log.info("批量新增波谱信息，数量: {}", waterSpectrumList.size());

        // 处理监测时间
        for (TWaterSpectrum waterSpectrum : waterSpectrumList) {
            if (waterSpectrum.getMonitoringDt() != null) {
                waterSpectrum.setMonitoringDt(DateTimeUtil.processMonitoringDateTime(waterSpectrum.getMonitoringDt()));
            }
        }

        int result = waterSpectrumMapper.batchInsert(waterSpectrumList);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("批量新增波谱信息");
        }

        log.info("批量新增波谱信息成功，实际插入数量: {}", result);
    }

    @Override
    public List<TWaterSpectrum> selectByRegionIds(List<Integer> regionIds) {
        if (regionIds == null || regionIds.isEmpty()) {
            log.warn("地物ID列表为空，返回空列表");
            return new ArrayList<>();
        }
        log.info("根据地物ID列表查询波谱信息: {}", regionIds);
        List<TWaterSpectrum> result = waterSpectrumMapper.selectByRegionIds(regionIds);
        log.info("查询完成，共查询到{}条波谱信息", result != null ? result.size() : 0);
        return result;
    }

    @Override
    public List<TWaterSpectrum> selectAll() {
        log.info("查询所有波谱信息");
        List<TWaterSpectrum> result = waterSpectrumMapper.selectAll();
        log.info("查询完成，共查询到{}条波谱信息", result != null ? result.size() : 0);
        return result;
    }

    /**
     * 根据地物ID删除波谱信息
     * @param regionId
     */
    public void deleteByRegionId(Integer regionId){
        int result = waterSpectrumMapper.deleteByRegionId(regionId);
        log.info("根据地物ID删除波谱信息: {}, 一共{}条记录", regionId, result);
    }
}
