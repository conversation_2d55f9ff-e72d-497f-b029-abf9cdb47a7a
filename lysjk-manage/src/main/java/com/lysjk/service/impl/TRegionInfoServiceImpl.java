package com.lysjk.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.TRegionInfoPageQueryDTO;
import com.lysjk.dto.TRegionInfoWithMonitoringPointsDTO;
import com.lysjk.entity.TRegionInfo;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.BusinessException;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.TRegionInfoMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.*;
import com.lysjk.utils.DateTimeUtil;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 地物信息服务实现类
 */
@Slf4j
@Service
@Transactional
public class TRegionInfoServiceImpl implements TRegionInfoService {
    
    @Autowired
    private TRegionInfoMapper regionInfoMapper;
    @Autowired
    private TWaterSpectrumService waterSpectrumService;
    @Autowired
    private TWaterQualityService waterQualityService;
    @Autowired
    private TMonitoringPointService monitoringPointService;
    @Autowired
    private TImageService imageService;
    @Autowired
    private TWaterEnvironmentService waterEnvironmentService;
    @Autowired
    private TMonitoringRecordService monitoringRecordService;

    /**
     * 新增地物信息
     * @param regionInfo 地物信息
     */
    @Override
    public void save(TRegionInfo regionInfo) {
        if (regionInfo == null) {
            throw new ValidationException.ParameterNullException("地物信息");
        }

        // 处理采样时间（如果未设置则使用当前时间）
        regionInfo.setSampleDt(DateTimeUtil.processSampleDateTime(regionInfo.getSampleDt()));

        // 都写出来是为了方便看看(如果填写的话)
        log.info("新增地物信息: id={}, code地物编码={}, name={}, sampleDt={}", regionInfo.getId(), regionInfo.getCode(), regionInfo.getName(), regionInfo.getSampleDt());

        // 检查数据库是否存在
        TRegionInfo existing = regionInfoMapper.selectByNameAndSampleDt(regionInfo.getName(), regionInfo.getSampleDt());
        if(existing != null){
            throw new BusinessException.OperationFailedException("地物信息已存在, 采集地点:" + regionInfo.getName() + ", 采样时间:" + regionInfo.getSampleDt());
        }

        int result = regionInfoMapper.insertSelective(regionInfo);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("新增地物信息");
        }
    }

    /**
     * 批量删除地物信息
     * @param ids ID列表
     */
    @Override
    public void deleteBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ValidationException.ParameterNullException("删除ID列表");
        }

        // 权限验证
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if (!RoleConstant.ADMIN.equals(role)) {
            throw new AuthenticationException.InsufficientPermissionException("删除地物信息");
        }

        log.info("批量删除地物信息: {}", ids);

        int result = regionInfoMapper.deleteByIds(ids);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("删除地物信息");
        }
    }

    /**
     * 更新地物信息
     * @param regionInfo 地物信息
     */
    @Override
    public void update(TRegionInfo regionInfo) {
        if (regionInfo == null) {
            throw new ValidationException.ParameterNullException("地物信息");
        }

        if (regionInfo.getId() == null) {
            throw new ValidationException.ParameterNullException("地物信息ID");
        }

        // 只有id是必传的,所以此时合理, 日期有的话也打印出来看看
        log.info("更新地物信息: id={}, 采样时间: {}", regionInfo.getId(), regionInfo.getSampleDt());

        int result = regionInfoMapper.updateBusinessFields(regionInfo);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("更新地物信息");
        }
    }

    @Override
    public TRegionInfo selectByPrimaryKey(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        return regionInfoMapper.selectByPrimaryKey(id);
    }
    
    @Override
    public List<TRegionInfo> selectByAdministrativeRegion(String sheng, String shi, String qu, String zhen) {
        log.info("根据行政区划查询地物信息 - 省:{}, 市:{}, 区:{}, 镇:{}", sheng, shi, qu, zhen);
        return regionInfoMapper.selectByAdministrativeRegion(sheng, shi, qu, zhen);
    }
    
    @Override
    public Point selectCenterById(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        Point center = regionInfoMapper.selectCenterById(id);
        log.info("查询地物ID:{}的中心点坐标:{}", id, center != null ? center.toString() : "null");
        return center;
    }
    
    @Override
    public MultiPolygon selectRegionById(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        MultiPolygon region = regionInfoMapper.selectRegionById(id);
        log.info("查询地物ID:{}的区域范围:{}", id, region != null ? region.toString() : "null");
        return region;
    }
    
    @Override
    public int insert(TRegionInfo record) {
        if (record == null) {
            throw new IllegalArgumentException("地物信息不能为空");
        }
        
        // 设置创建信息
        setCreateInfo(record);
        
        int result = regionInfoMapper.insert(record);
        log.info("新增地物信息成功，ID:{}", record.getId());
        return result;
    }
    
    @Override
    public int insertSelective(TRegionInfo record) {
        if (record == null) {
            throw new IllegalArgumentException("地物信息不能为空");
        }
        
        // 设置创建信息
        setCreateInfo(record);
        
        int result = regionInfoMapper.insertSelective(record);
        log.info("选择性新增地物信息成功，ID:{}", record.getId());
        return result;
    }
    
    @Override
    public int deleteByPrimaryKey(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        
        // 检查记录是否存在
        TRegionInfo existing = regionInfoMapper.selectByPrimaryKey(id);
        if (existing == null) {
            throw new IllegalArgumentException("地物信息不存在，ID:" + id);
        }
        
        int result = regionInfoMapper.deleteByPrimaryKey(id);
        log.info("删除地物信息成功，ID:{}", id);
        return result;
    }
    
    @Override
    public int updateCenterById(Integer id, Point center) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        if (center == null) {
            throw new IllegalArgumentException("中心点坐标不能为空");
        }
        
        // 检查记录是否存在
        TRegionInfo existing = regionInfoMapper.selectByPrimaryKey(id);
        if (existing == null) {
            throw new IllegalArgumentException("地物信息不存在，ID:" + id);
        }
        
        int result = regionInfoMapper.updateCenterById(id, center);
        log.info("更新地物ID:{}的中心点坐标成功:{}", id, center.toString());
        return result;
    }
    
    @Override
    public int updateRegionById(Integer id, MultiPolygon region) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        if (region == null) {
            throw new IllegalArgumentException("区域范围不能为空");
        }
        
        // 检查记录是否存在
        TRegionInfo existing = regionInfoMapper.selectByPrimaryKey(id);
        if (existing == null) {
            throw new IllegalArgumentException("地物信息不存在，ID:" + id);
        }
        
        int result = regionInfoMapper.updateRegionById(id, region);
        log.info("更新地物ID:{}的区域范围成功:{}", id, region.toString());
        return result;
    }
    
    @Override
    public int updateBusinessFields(TRegionInfo record) {
        if (record == null || record.getId() == null) {
            throw new IllegalArgumentException("地物信息或ID不能为空");
        }
        
        // 检查记录是否存在
        TRegionInfo existing = regionInfoMapper.selectByPrimaryKey(record.getId());
        if (existing == null) {
            throw new IllegalArgumentException("地物信息不存在，ID:" + record.getId());
        }
        
        // 设置更新信息
        setUpdateInfo(record);
        
        int result = regionInfoMapper.updateBusinessFields(record);
        log.info("更新地物业务字段成功，ID:{}", record.getId());
        return result;
    }

    @Override
    public PageResult selectPage(TRegionInfoPageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页查询地物信息: page={}, pageSize={}, name={}, code={}",
                pageQueryDTO.getPage(), pageQueryDTO.getPageSize(),
                pageQueryDTO.getName(), pageQueryDTO.getCode());

        // 开启分页
        PageHelper.startPage(pageQueryDTO.getPage(), pageQueryDTO.getPageSize());

        // 执行查询
        Page<TRegionInfo> page = regionInfoMapper.pageQuery(pageQueryDTO);

        // 封装结果
        long total = page.getTotal();
        List<TRegionInfo> result = page.getResult();

        log.info("分页查询完成，共查询到{}条记录", total);
        return new PageResult(total, result);
    }

    @Override
    public int updateByPrimaryKey(TRegionInfo record) {
        if (record == null || record.getId() == null) {
            throw new IllegalArgumentException("地物信息或ID不能为空");
        }
        
        // 设置更新信息
        setUpdateInfo(record);
        
        int result = regionInfoMapper.updateByPrimaryKey(record);
        log.info("全量更新地物信息成功，ID:{}", record.getId());
        return result;
    }
    
    /**
     * 设置创建信息
     */
    private void setCreateInfo(TRegionInfo record) {
        LocalDateTime now = LocalDateTime.now();
        record.setCreateDt(now);
        record.setUpdateDt(now);
        
        try {
            Map<String, Object> claims = ThreadLocalUtil.get();
            if (claims != null) {
                Integer userId = (Integer) claims.get("uid");
                record.setCreateBy(userId);
                record.setUpdateBy(userId);
            }
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认值", e);
            // 如果获取用户信息失败，可以设置默认值或者抛出异常
        }
    }
    
    /**
     * 设置更新信息
     */
    private void setUpdateInfo(TRegionInfo record) {
        record.setUpdateDt(LocalDateTime.now());
        
        try {
            Map<String, Object> claims = ThreadLocalUtil.get();
            if (claims != null) {
                Integer userId = (Integer) claims.get("uid");
                record.setUpdateBy(userId);
            }
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认值", e);
        }
    }

    @Override
    public List<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPoints() {
        log.info("查询地物信息及其关联的监测点列表");
        List<TRegionInfoWithMonitoringPointsDTO> result = regionInfoMapper.selectRegionInfoWithMonitoringPoints();
        log.info("查询完成，共查询到{}条地物信息", result != null ? result.size() : 0);
        return result;
    }

    @Override
    public List<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPointsById(Integer regionId) {
        if (regionId == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        log.info("根据地物ID查询地物信息及其关联的监测点列表: {}", regionId);
        List<TRegionInfoWithMonitoringPointsDTO> result = regionInfoMapper.selectRegionInfoWithMonitoringPointsById(regionId);
        if (result != null) {
            log.info("查询关联的内容成功");
        } else {
            log.info("未找到ID为{}的地物信息", regionId);
        }
        return result;
    }

    @Override
    public PageInfo<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPointsPage(int pageNum, int pageSize) {
        log.info("分页查询地物信息及其关联的监测点列表 - 页码:{}, 每页大小:{}", pageNum, pageSize);

        // 参数验证
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1 || pageSize > 100) {
            pageSize = 10;
        }

        // 启动分页
        PageHelper.startPage(pageNum, pageSize);

        // 执行查询
        List<TRegionInfoWithMonitoringPointsDTO> list = regionInfoMapper.selectRegionInfoWithMonitoringPointsForPage();

        // 封装分页结果
        PageInfo<TRegionInfoWithMonitoringPointsDTO> pageInfo = PageInfo.of(list);

        log.info("分页查询完成 - 总记录数:{}, 总页数:{}, 当前页:{}",
                pageInfo.getTotal(), pageInfo.getPages(), pageInfo.getPageNum());

        return pageInfo;
    }

    @Override
    public TRegionInfo selectByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("地物名称不能为空");
        }
        log.info("根据地物名称查询地物信息: {}", name);
        TRegionInfo result = regionInfoMapper.selectByName(name.trim());
        if (result != null) {
            log.info("查询到地物信息: id={}, code={}", result.getId(), result.getCode());
        } else {
            log.info("未找到名称为{}的地物信息", name);
        }
        return result;
    }

    /**
     * 批量新增地物信息
     * @param regionInfoList 地物信息列表
     */
    @Override
    public void batchInsert(List<TRegionInfo> regionInfoList) {
        if (regionInfoList == null || regionInfoList.isEmpty()) {
            throw new ValidationException.ParameterNullException("地物信息列表");
        }

        log.info("批量新增地物信息，数量: {}", regionInfoList.size());

        // 处理采样时间
        for (TRegionInfo regionInfo : regionInfoList) {
            if (regionInfo.getSampleDt() != null) {
                regionInfo.setSampleDt(DateTimeUtil.processSampleDateTime(regionInfo.getSampleDt()));
            }
        }

        // 处理创建信息
        for (TRegionInfo regionInfo : regionInfoList) {
            TRegionInfo existing = regionInfoMapper.selectByNameAndSampleDt(regionInfo.getName(), regionInfo.getSampleDt());
            if(existing != null){
                throw new BusinessException.OperationFailedException("地物信息已存在, 采集地点:" + regionInfo.getName() + ", 采样时间:" + regionInfo.getSampleDt());
            }
        }

        int result = regionInfoMapper.batchInsert(regionInfoList);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("批量新增地物信息");
        }

        log.info("批量新增地物信息成功，实际插入数量: {}", result);
    }

    @Override
    public List<TRegionInfo> selectByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("ID列表为空，返回空列表");
            return new ArrayList<>();
        }
        log.info("根据ID列表查询地物信息: {}", ids);
        List<TRegionInfo> result = regionInfoMapper.selectByIds(ids);
        log.info("查询完成，共查询到{}条地物信息", result != null ? result.size() : 0);
        return result;
    }

    @Override
    public List<TRegionInfo> selectAll() {
        log.info("查询所有地物信息");
        List<TRegionInfo> result = regionInfoMapper.selectAll();
        log.info("查询完成，共查询到{}条地物信息", result != null ? result.size() : 0);
        return result;
    }

    /**
     * 删除地物表以及关联的其他数据
     * 这个删除比较重要,出异常就回滚
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteWithAssociated(Integer id){
        // 进行权限判断
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if (!RoleConstant.ADMIN.equals(role)) {
            throw new AuthenticationException.InsufficientPermissionException("删除地物信息");
        }

        if(id == null){
            throw new IllegalArgumentException("地物ID不能为空");
        }
        // 删除地物表
        int result = regionInfoMapper.deleteByPrimaryKey(id);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("删除地物信息");
        }

        monitoringPointService.deleteByRegionId(id);
        waterEnvironmentService.deleteByRegionId(id);
        waterQualityService.deleteByRegionId(id);
        waterSpectrumService.deleteByRegionId(id);
        imageService.deleteByRegionId(id);
        monitoringRecordService.deleteByRegionId(id);
    }

    /**
     * 根据采集地点和监测时间查询,应该只能查到一个,数据库也是这样存储的
     */
    public TRegionInfo selectByExcelNameAndSampleDt(String name, LocalDateTime sampleDt){
        return regionInfoMapper.selectByNameAndSampleDt(name, sampleDt);
    }
}
