package com.lysjk.service;

import org.springframework.core.io.Resource;

import java.util.List;

/**
 * Excel导出服务接口
 * 提供完整的Excel文件导出功能，支持导出水质监测数据到Excel文件中
 * 导出格式与导入模板完全一致，包含7个工作表
 */
public interface ExcelExportService {

    /**
     * 导出完整的Excel文件
     * 包含所有7个工作表：主表、监测点信息表、环境参数表、水质参数表、反射率光谱表、测量记录表、影像信息表
     *
     * @param regionIds 地物ID列表，如果为空则导出所有数据
     * @return Excel文件资源
     */
    Resource exportCompleteExcel(List<Integer> regionIds);

    /**
     * 导出地物信息主表
     *
     * @param regionIds 地物ID列表，如果为空则导出所有数据
     * @return Excel文件资源
     */
    Resource exportRegionInfoMain(List<Integer> regionIds);

    /**
     * 导出监测点信息表
     *
     * @param regionIds 地物ID列表，如果为空则导出所有数据
     * @return Excel文件资源
     */
    Resource exportMonitoringPoint(List<Integer> regionIds);

    /**
     * 导出环境参数表
     *
     * @param regionIds 地物ID列表，如果为空则导出所有数据
     * @return Excel文件资源
     */
    Resource exportWaterEnvironment(List<Integer> regionIds);

    /**
     * 导出水质参数表
     *
     * @param regionIds 地物ID列表，如果为空则导出所有数据
     * @return Excel文件资源
     */
    Resource exportWaterQuality(List<Integer> regionIds);

    /**
     * 导出反射率光谱表
     *
     * @param regionIds 地物ID列表，如果为空则导出所有数据
     * @return Excel文件资源
     */
    Resource exportWaterSpectrum(List<Integer> regionIds);

    /**
     * 导出测量记录表
     *
     * @param regionIds 地物ID列表，如果为空则导出所有数据
     * @return Excel文件资源
     */
    Resource exportMonitoringRecord(List<Integer> regionIds);

    /**
     * 导出影像信息表
     *
     * @param regionIds 地物ID列表，如果为空则导出所有数据
     * @return Excel文件资源
     */
    Resource exportImage(List<Integer> regionIds);
}
