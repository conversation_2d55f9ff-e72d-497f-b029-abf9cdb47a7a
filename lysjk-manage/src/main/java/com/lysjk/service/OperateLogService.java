package com.lysjk.service;

import com.lysjk.dto.OperateLogPageQueryDTO;
import com.lysjk.entity.OperateLog;
import com.lysjk.result.PageResult;

import java.util.List;

/**
 * 操作日志服务接口
 */
public interface OperateLogService {

    /**
     * 批量删除操作日志
     * @param ids ID列表
     */
    void deleteBatch(List<Integer> ids);

    /**
     * 分页条件查询操作日志
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    PageResult selectPage(OperateLogPageQueryDTO pageQueryDTO);
}
