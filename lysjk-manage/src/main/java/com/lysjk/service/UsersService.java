package com.lysjk.service;

import com.github.pagehelper.PageInfo;
import com.lysjk.vo.LoginInfoVO;
import com.lysjk.entity.Users;
import com.lysjk.vo.UsersVO;

import java.util.List;

public interface UsersService {
    LoginInfoVO login(Users users);
    Users selectByUsername(String username);
    void createUser(String username, String password, String nickname, String defaultPro);
    void register(String username, String password, String nickname, String defaultPr);
    void update(Users users);
    void updatePwdByUid(Integer uid, String encryptPwd);
    void deleteUser(Integer uid);
    List<Users> selectAll();
    PageInfo<Users> selectPage(Users users, Integer pageNum, Integer pageSize);
    void updateUser(Users users);
    List<Users> selectByUsernameLike(String username);

    /**
     * 测试查询用户信息返回给前端
     * @param username
     * @param nickname
     * @return
     */
    List<UsersVO> list(String username, String nickname);

    /**
     * 测试处理文件操作
     * @param usersVOList
     */
    void saveBatch(List<UsersVO> usersVOList);

    /**
     * 批量新增用户信息
     * @param usersList 用户信息列表
     */
    void batchInsert(List<Users> usersList);
}
