package com.lysjk.service;

import com.lysjk.dto.excel.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Excel解析服务接口
 */
public interface ExcelParseService {

    /**
     * 解析Excel文件并录入数据库
     * 
     * @param file Excel文件
     * @return 解析结果信息
     */
    String parseAndSaveExcel(MultipartFile file);

    /**
     * 解析地物信息主表数据
     * 
     * @param file Excel文件
     * @return 地物信息列表
     */
    List<TRegionInfoMainExcelDTO> parseRegionInfoMain(MultipartFile file);

    /**
     * 解析监测点信息表数据
     * 
     * @param file Excel文件
     * @return 监测点信息列表
     */
    List<TMonitoringPointExcelDTO> parseMonitoringPoint(MultipartFile file);

    /**
     * 解析水域环境表数据
     * 
     * @param file Excel文件
     * @return 水域环境信息列表
     */
    List<TWaterEnvironmentExcelDTO> parseWaterEnvironment(MultipartFile file);

    /**
     * 解析水质参数表数据
     * 
     * @param file Excel文件
     * @return 水质参数信息列表
     */
    List<TWaterQualityExcelDTO> parseWaterQuality(MultipartFile file);

    /**
     * 解析反射率光谱表数据
     * 
     * @param file Excel文件
     * @return 反射率光谱信息列表
     */
    List<TWaterSpectrumExcelDTO> parseWaterSpectrum(MultipartFile file);

    /**
     * 解析测量记录表数据
     * 
     * @param file Excel文件
     * @return 测量记录信息列表
     */
    List<TMonitoringRecordExcelDTO> parseMonitoringRecord(MultipartFile file);

    /**
     * 解析影像信息表数据
     * 
     * @param file Excel文件
     * @return 影像信息列表
     */
    List<TImageExcelDTO> parseImage(MultipartFile file);
}
