package com.lysjk.aspect;

import com.lysjk.anno.AutoFill;
import com.lysjk.constant.AutoFillConstant;
import com.lysjk.enums.Operation;
import com.lysjk.utils.ThreadLocalUtil;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 自定义切面,实现公共字段自动填充逻辑,拦截注解使用
 * 这里只不过用反射设置了,所以还是要加上注解在数据库中进一步赋值
 * 注意主要是写在mapper中,而且mapper中默认第一个字段含有封装的信息(所以新增和修改一般传递的就是json格式居多)
 * 反正可以先DTO,但是最后的mapper传递的都是完整的数据库实体对象
 */
@Aspect
@Component // 交给Spring容器扫描管理
@Slf4j
public class AutoFillAspect {
    // 指定通知加上切入点
    /**
     * 切入点
     * 切入点表达式
     * 返回值所有的*,拦截mapper包下所有类,所有方法,匹配所有参数类型
     * 直接@annotation也是可以的,不过前面加上这个可以精确扫到包,提升了一点点性能
     */
    @Pointcut("execution(* com.lysjk.mapper.*.*(..)) && @annotation(com.lysjk.anno.AutoFill)")
    public void autoFillPointCut(){}

    /**
     * 在通知当中为公共字段赋值(环绕通知@Around更常用)
     * 肯定是匹配到切入点表达式就执行该方法
     * 支持单个实体对象和List<实体对象>两种参数类型
     */
    @Before("autoFillPointCut()")
    public void autoFill(JoinPoint joinPoint){
        log.info("开始进行公共字段的自动填充...");

        // 1.先获取被拦截的方法上数据库的操作类型是什么(添加注解所写得value值)
        // 肯定是对有多态的对象进行向下转型(强转), 如果小范围转大范围(自动,比如数据类型)
        // 这其中肯定先发生了向上转型,才能向下转型
        MethodSignature signature = (MethodSignature) joinPoint.getSignature(); // 可以向下转型一下获取方法签名对象
        AutoFill autoFill = signature.getMethod().getAnnotation(AutoFill.class);
        Operation operationType = autoFill.value(); // 进一步获取数据库操作类型

        // 2.获取当前被拦截的方法参数--实体对象
        Object[] args = joinPoint.getArgs(); // 约定将实体对象我们都写到第一个,这样方便拿取
        if (args == null || args.length == 0) return; // 如果没有参数就直接返回
        Object entity = args[0]; // 因为不知道什么类型,所以用Object来接受

        // 3.先准备赋值的数据(可以先用变量来接受)
        LocalDateTime now = LocalDateTime.now();
        Map<String, Object> claims = ThreadLocalUtil.get();
        Integer loginId = (Integer)claims.get("uid");

        // 4.判断参数类型并进行相应处理
        if (entity instanceof List) {
            // 如果是List类型，为列表中的每个实体对象设置公共字段
            List<?> entityList = (List<?>) entity;
            if (!entityList.isEmpty()) {
                for (Object item : entityList) {
                    fillCommonFields(item, operationType, now, loginId);
                }
            }
        } else {
            // 如果是单个实体对象，直接设置公共字段
            fillCommonFields(entity, operationType, now, loginId);
        }
    }

    /**
     * 为单个实体对象填充公共字段
     * @param entity 实体对象
     * @param operationType 操作类型
     * @param now 当前时间
     * @param loginId 当前登录用户ID
     */
    private void fillCommonFields(Object entity, Operation operationType, LocalDateTime now, Integer loginId) {
        // 根据操作类型为对应的属性通过反射来赋值
        if(Operation.INSERT.equals(operationType)){
            // 如果是插入操作,四个字段都需要赋值
            // 反射先获取指定的方法
            try {
                Method setCreateTime = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_CREATE_TIME, LocalDateTime.class);
                Method setUpdateTime = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_TIME, LocalDateTime.class);
                Method setCreateUser = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_CREATE_USER, Integer.class);
                Method setUpdateUser = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_USER, Integer.class);
                // 调用方法赋值,反射反过来赋值
                setCreateTime.invoke(entity, now);
                setUpdateTime.invoke(entity, now);
                setCreateUser.invoke(entity, loginId);
                setUpdateUser.invoke(entity, loginId);

            } catch (Exception e) {
                log.warn("为实体对象{}设置创建字段失败: {}", entity.getClass().getSimpleName(), e.getMessage());
            }

        }else if(Operation.UPDATE.equals(operationType)){
            // 如果是更新操作,只有修改时间,修改人需要赋值,用常量类更优雅
            try {
//                entity.getClass().getDeclaredMethod("setUpdateTime", LocalDateTime.class).invoke(entity, now);
//                entity.getClass().getDeclaredMethod("setUpdateUser", Integer.class).invoke(entity, loginId);
                entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_TIME, LocalDateTime.class).invoke(entity, now);
                entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_USER, Integer.class).invoke(entity, loginId);
            } catch (Exception e) {
                log.warn("为实体对象{}设置更新字段失败: {}", entity.getClass().getSimpleName(), e.getMessage());
                throw new RuntimeException(e);
            }
        }
    }
}
