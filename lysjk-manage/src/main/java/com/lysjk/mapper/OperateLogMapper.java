package com.lysjk.mapper;

import com.github.pagehelper.Page;
import com.lysjk.dto.OperateLogPageQueryDTO;
import com.lysjk.entity.OperateLog;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 操作日志Mapper接口
 */
@Mapper
public interface OperateLogMapper {

    /**
     * 插入操作日志
     */
    @Insert("INSERT INTO operate_log (user_id, username, operation_name, operation_type, operation_url, operate_time, cost_time) " +
            "VALUES (#{userId}, #{username}, #{operationName}, #{operationType}, #{operationUrl}, #{operateTime}, #{costTime})")
    void insert(OperateLog log);

    /**
     * 批量删除操作日志
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteBatch(List<Integer> ids);

    /**
     * 分页条件查询操作日志
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    Page<OperateLog> pageQuery(OperateLogPageQueryDTO pageQueryDTO);
}
