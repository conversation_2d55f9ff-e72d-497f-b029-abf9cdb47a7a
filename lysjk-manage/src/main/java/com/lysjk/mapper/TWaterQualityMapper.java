package com.lysjk.mapper;

import com.github.pagehelper.Page;
import com.lysjk.anno.AutoFill;
import com.lysjk.dto.TWaterQualityPageQueryDTO;
import com.lysjk.entity.TWaterQuality;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 水质信息Mapper接口
 */
@Mapper
public interface TWaterQualityMapper {

    /**
     * 新增水质信息
     * @param waterQuality 水质信息
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int insert(TWaterQuality waterQuality);

    /**
     * 批量删除水质信息
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(List<Integer> ids);

    /**
     * 更新水质信息
     * @param waterQuality 水质信息
     * @return 影响行数
     */
    @AutoFill(Operation.UPDATE)
    int update(TWaterQuality waterQuality);

    /**
     * 分页查询水质信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    Page<TWaterQuality> pageQuery(TWaterQualityPageQueryDTO pageQueryDTO);

    /**
     * 批量新增水质信息
     * @param waterQualityList 水质信息列表
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int batchInsert(List<TWaterQuality> waterQualityList);

    /**
     * 根据地物ID列表查询水质信息
     * @param regionIds 地物ID列表
     * @return 水质信息列表
     */
    List<TWaterQuality> selectByRegionIds(@Param("regionIds") List<Integer> regionIds);

    /**
     * 查询所有水质信息
     * @return 水质信息列表
     */
    List<TWaterQuality> selectAll();

    /**
     * 根据地物ID删除水质信息
     * @param regionId
     * @return
     */
    @Delete("delete from t_water_quality where region_id = #{regionId}")
    int deleteByRegionId(Integer regionId);
}
