package com.lysjk.mapper;

import com.github.pagehelper.Page;
import com.lysjk.anno.AutoFill;
import com.lysjk.dto.TMonitoringRecordPageQueryDTO;
import com.lysjk.entity.TMonitoringRecord;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 测量记录Mapper接口
 */
@Mapper
public interface TMonitoringRecordMapper {
    
    /**
     * 新增测量记录
     * @param monitoringRecord 测量记录信息
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int insert(TMonitoringRecord monitoringRecord);
    
    /**
     * 批量删除测量记录
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(List<Integer> ids);
    
    /**
     * 更新测量记录
     * @param monitoringRecord 测量记录信息
     * @return 影响行数
     */
    @AutoFill(Operation.UPDATE)
    int update(TMonitoringRecord monitoringRecord);
    
    /**
     * 分页查询测量记录
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    Page<TMonitoringRecord> pageQuery(TMonitoringRecordPageQueryDTO pageQueryDTO);

    /**
     * 批量新增测量记录
     * @param monitoringRecordList 测量记录列表
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int batchInsert(List<TMonitoringRecord> monitoringRecordList);

    /**
     * 根据地物ID列表查询测量记录
     * @param regionIds 地物ID列表
     * @return 测量记录列表
     */
    List<TMonitoringRecord> selectByRegionIds(@Param("regionIds") List<Integer> regionIds);

    /**
     * 查询所有测量记录
     * @return 测量记录列表
     */
    List<TMonitoringRecord> selectAll();

    /**
     * 根据地物ID删除测量记录
     * @param regionId
     * @return
     */
    @Delete("delete from t_monitoring_record where region_id = #{regionId}")
    int deleteByRegionId(Integer regionId);
}
