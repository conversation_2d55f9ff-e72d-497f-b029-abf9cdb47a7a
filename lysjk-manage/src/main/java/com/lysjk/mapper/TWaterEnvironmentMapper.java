package com.lysjk.mapper;

import com.github.pagehelper.Page;
import com.lysjk.anno.AutoFill;
import com.lysjk.dto.TWaterEnvironmentPageQueryDTO;
import com.lysjk.entity.TWaterEnvironment;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 水域环境信息Mapper接口
 */
@Mapper
public interface TWaterEnvironmentMapper {
    
    /**
     * 新增水域环境信息
     * @param waterEnvironment 水域环境信息
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int insert(TWaterEnvironment waterEnvironment);
    
    /**
     * 批量删除水域环境信息
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(List<Integer> ids);
    
    /**
     * 更新水域环境信息
     * @param waterEnvironment 水域环境信息
     * @return 影响行数
     */
    @AutoFill(Operation.UPDATE)
    int update(TWaterEnvironment waterEnvironment);
    
    /**
     * 分页查询水域环境信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    Page<TWaterEnvironment> pageQuery(TWaterEnvironmentPageQueryDTO pageQueryDTO);

    /**
     * 批量新增水域环境信息
     * @param waterEnvironmentList 水域环境信息列表
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int batchInsert(List<TWaterEnvironment> waterEnvironmentList);

    /**
     * 根据地物ID列表查询水域环境信息
     * @param regionIds 地物ID列表
     * @return 水域环境信息列表
     */
    List<TWaterEnvironment> selectByRegionIds(@Param("regionIds") List<Integer> regionIds);

    /**
     * 查询所有水域环境信息
     * @return 水域环境信息列表
     */
    List<TWaterEnvironment> selectAll();

    /**
     * 根据地物ID删除水域环境信息
     * @param regionId
     * @return
     */
    @Delete("delete from t_water_environment where region_id = #{regionId}")
    int deleteByRegionId(Integer regionId);
}
