package com.lysjk.controller;

import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.OperateLogPageQueryDTO;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.ValidationException;
import com.lysjk.result.PageResult;
import com.lysjk.result.Result;
import com.lysjk.service.OperateLogService;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 操作日志控制层
 */
@Slf4j
@RestController
@RequestMapping("/operateLog")
public class OperateLogController {

    @Autowired
    private OperateLogService operateLogService;

    /**
     * 批量删除操作日志
     * 只有管理员才能删除操作日志
     * @param ids 操作日志ID列表
     * @return 操作结果
     */
    @DeleteMapping("/deleteBatch")
    public Result deleteBatch(@RequestParam List<Integer> ids) {
        // 权限检查：只有管理员才能删除操作日志
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if (!RoleConstant.ADMIN.equals(role)) {
            throw new AuthenticationException.InsufficientPermissionException();
        }

        // 参数验证
        if (ids == null || ids.isEmpty()) {
            throw new ValidationException.ParameterNullException("操作日志ID列表");
        }

        log.info("管理员批量删除操作日志，ID列表: {}", ids);

        // 执行批量删除
        operateLogService.deleteBatch(ids);

        log.info("批量删除操作日志成功，共删除{}条记录", ids.size());
        return Result.success();
    }

    /**
     * 分页条件查询操作日志
     * 支持按用户名、操作类型、操作名称、请求URL、操作时间范围等条件查询
     * @param pageQueryDTO 分页查询条件
     * @return 分页查询结果
     */
    @GetMapping("/page")
    public Result<PageResult> selectPage(OperateLogPageQueryDTO pageQueryDTO) {
        // 参数验证
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页条件查询操作日志: {}", pageQueryDTO);

        // 执行分页查询
        PageResult pageResult = operateLogService.selectPage(pageQueryDTO);

        log.info("分页查询操作日志完成，共查询到{}条记录", pageResult.getTotal());
        return Result.success(pageResult);
    }
}
