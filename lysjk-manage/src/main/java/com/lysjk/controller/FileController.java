package com.lysjk.controller;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.lysjk.anno.LogOperation;
import com.lysjk.constant.ExcelConstant;
import com.lysjk.constant.RoleConstant;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.FileOperationException;
import com.lysjk.exception.ValidationException;
import com.lysjk.result.Result;
import com.lysjk.service.FileService;
import com.lysjk.service.UsersService;
import com.lysjk.utils.ThreadLocalUtil;
import com.lysjk.vo.UsersVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 文件控制层
 * 处理文件上传、下载等操作
 */
@Slf4j
@RestController
@RequestMapping("/file")
public class FileController {

    @Autowired
    private FileService fileService;
    @Autowired
    private UsersService usersService;

    /**
     * 文件上传
     * @param file 上传的文件
     * @return 上传结果
     */
    @LogOperation
    @PostMapping("/upload")
    public Result<String> fileUpload(@RequestParam("file") MultipartFile file) {
        String fileName = fileService.uploadFile(file);
        log.info("文件上传成功: {}", fileName);
        return Result.success(fileName);
    }

    /**
     * 文件下载
     * @param fileName 文件名
     * @return 文件内容
     */
    @LogOperation
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadFile(@RequestParam String fileName) {
        try {
            // 验证文件名
            if (fileName == null || fileName.trim().isEmpty()) {
                throw new ValidationException.ParameterNullException("文件名");
            }

            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                throw new ValidationException.ParameterFormatException("文件名", "不能包含路径分隔符");
            }

            // 检查文件是否存在
            if (!fileService.fileExists(fileName)) {
                throw new FileOperationException.FileNotFoundException(fileName);
            }

            // 获取文件
            String filePath = fileService.getFilePath(fileName);
            File file = new File(filePath);
            Resource resource = new FileSystemResource(file);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();

            // 设置正确的Content-Type
            String contentType = fileService.getContentType(fileName);
            headers.setContentType(MediaType.parseMediaType(contentType));

            // 设置文件下载名称，确保文件名和扩展名正确显示
            // 使用多种格式确保最大兼容性
            try {
                // 方法1：标准的RFC 5987格式（支持UTF-8）
                String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                        .replaceAll("\\+", "%20");

                // 方法2：同时设置filename和filename*以确保兼容性
                String dispositionValue = String.format(
                    "attachment; filename=\"%s\"; filename*=UTF-8''%s",
                    fileName.replaceAll("\"", "\\\""), // 转义双引号
                    encodedFileName
                );

                headers.add(HttpHeaders.CONTENT_DISPOSITION, dispositionValue);

                // 额外设置，确保文件名正确传递
                headers.add("X-Suggested-Filename", fileName);

            } catch (Exception e) {
                // 如果编码失败，使用简单格式
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            }

            // 设置文件大小
            headers.setContentLength(file.length());

            // 设置缓存控制
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.add("Pragma", "no-cache");
            headers.add("Expires", "0");

            // 添加额外的响应头来确保文件类型正确识别
            headers.add("X-Content-Type-Options", "nosniff");
            headers.add("Content-Transfer-Encoding", "binary");

            log.info("文件下载成功: {}", fileName);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);

        } catch (ValidationException | FileOperationException e) {
            log.warn("文件下载失败: {}", e.getMsg());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(null);
        } catch (Exception e) {
            log.error("文件下载异常: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }

    /**
     * 文件下载（备用方法，返回字节流）
     * 如果上面的方法在某些客户端不工作，可以尝试这个方法
     * @param fileName 文件名
     * @return 文件字节流
     */
    @LogOperation
    @GetMapping("/download-bytes")
    public ResponseEntity<byte[]> downloadFileAsBytes(@RequestParam String fileName) {
        try {
            // 验证文件名
            if (fileName == null || fileName.trim().isEmpty()) {
                throw new ValidationException.ParameterNullException("文件名");
            }

            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                throw new ValidationException.ParameterFormatException("文件名", "不能包含路径分隔符");
            }

            // 检查文件是否存在
            if (!fileService.fileExists(fileName)) {
                throw new FileOperationException.FileNotFoundException(fileName);
            }

            // 读取文件
            String filePath = fileService.getFilePath(fileName);
            File file = new File(filePath);
            byte[] fileBytes = java.nio.file.Files.readAllBytes(file.toPath());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();

            // 设置正确的Content-Type
            String contentType = fileService.getContentType(fileName);
            headers.setContentType(MediaType.parseMediaType(contentType));

            // 使用最简单但最兼容的文件名设置方式
            headers.add("Content-Disposition",
                String.format("attachment; filename=\"%s\"", fileName));

            // 设置文件大小
            headers.setContentLength(fileBytes.length);

            log.info("文件下载成功（字节流方式）: {}", fileName);
            return new ResponseEntity<>(fileBytes, headers, HttpStatus.OK);

        } catch (ValidationException | FileOperationException e) {
            log.warn("文件下载失败: {}", e.getMsg());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        } catch (Exception e) {
            log.error("文件下载异常: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 删除文件
     * @param fileName 文件名
     * @return 删除结果
     */
    @LogOperation
    @DeleteMapping("/delete")
    public Result deleteFile(@RequestParam String fileName) {
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if(!role.equals(RoleConstant.ADMIN)){
            throw new AuthenticationException.InsufficientPermissionException();
        }

        try {
            // 验证文件名
            if (fileName == null || fileName.trim().isEmpty()) {
                throw new ValidationException.ParameterNullException("文件名");
            }

            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                throw new ValidationException.ParameterFormatException("文件名", "不能包含路径分隔符");
            }

            // 检查文件是否存在
            if (!fileService.fileExists(fileName)) {
                throw new FileOperationException.FileNotFoundException(fileName);
            }

            // 删除文件
            boolean deleted = fileService.deleteFile(fileName);
            if (deleted) {
                log.info("文件删除成功: {}", fileName);
                return Result.success();
            } else {
                log.warn("文件删除失败: {}", fileName);
                return Result.error("文件删除失败");
            }

        } catch (ValidationException | FileOperationException e) {
            log.warn("文件删除失败: {}", e.getMsg());
            return Result.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("文件删除异常: {}", fileName, e);
            return Result.error("文件删除失败");
        }
    }

    /**
     * 批量导出数据(肯定是Get请求来下载的)
     * 因为是通过输出流来处理的,所以不需要返回值(也不太一定)
     * @RequestParam(required = false) 暂时不写应该也可以
     */
    // TODO: 应该也可以分页导出数据,看看需不需要加上,查询条件多也可以封装为对象
    @LogOperation(operationName = "测试导出用户表数据")
    @GetMapping("/export")
    public void exportData(String username, String nickname, HttpServletResponse response) {
        ExcelWriter writer = ExcelUtil.getWriter(true);
        // 设置标题名称
        writer.addHeaderAlias("uid", "序号");
        writer.addHeaderAlias("username", "用户名");
        writer.addHeaderAlias("nickname", "昵称");
        writer.addHeaderAlias("role", "角色");
        writer.addHeaderAlias("phone", "手机号");
        writer.addHeaderAlias("email", "邮箱");
        writer.addHeaderAlias("defaultPro", "默认项目");
        writer.addHeaderAlias("createDt", "创建时间");
        writer.addHeaderAlias("updateDt", "更新时间");
        writer.addHeaderAlias("lastLoginTime", "最后登录时间");

        // 设置宽度
        writer.setColumnWidth(1, 20);
        writer.setColumnWidth(2, 20);
        writer.setColumnWidth(4, 20);
        writer.setColumnWidth(5, 20);
        writer.setColumnWidth(6, 20);
        writer.setColumnWidth(7, 20);
        writer.setColumnWidth(8, 20);
        writer.setColumnWidth(9, 20);

        // 查询数据库中的数据
        List<UsersVO> usersList = new ArrayList<>(); // 变量,其实也可以写类成员变量
        // 方式一: 直接动态sql就行
        usersList = usersService.list(username, nickname);
        writer.write(usersList, true); // true表示表头信息也写进去
        // 设置响应头以及文件名称
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + ExcelConstant.USER_EXPORT_FILE_NAME + ".xlsx");
        try (BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream())) {
            writer.flush(bos, true);
            bos.flush();
            writer.close(); // 也记得写
        } catch (Exception e) {
            throw new FileOperationException.FileExportException(e);
        }
    }

    /**
     * 批量导入
     * @param file
     * @return
     */
    @PostMapping("/import")
    public Result importData(MultipartFile file){
        try(BufferedInputStream bis = new BufferedInputStream(file.getInputStream())) {
            ExcelReader reader = ExcelUtil.getReader(bis);

            // 正确读取别名
            reader.addHeaderAlias("序号", "uid");
            reader.addHeaderAlias("用户名", "username");
            reader.addHeaderAlias("昵称", "nickname");
            reader.addHeaderAlias("角色", "role");
            reader.addHeaderAlias("手机号", "phone");
            reader.addHeaderAlias("邮箱", "email");
            reader.addHeaderAlias("默认项目", "defaultPro");
            reader.addHeaderAlias("创建时间", "createDt");
            reader.addHeaderAlias("更新时间", "updateDt");
            reader.addHeaderAlias("最后登录时间", "lastLoginTime");

            List<UsersVO> usersVOList = reader.readAll(UsersVO.class);
            // 然后写入数据到数据库
            usersService.saveBatch(usersVOList);
            reader.close();
        } catch (IOException e) {
            throw new FileOperationException.FileImportException(e);
        }
        return Result.success();
    }
}
