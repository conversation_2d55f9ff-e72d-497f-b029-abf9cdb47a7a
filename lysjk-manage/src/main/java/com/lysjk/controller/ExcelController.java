package com.lysjk.controller;

import com.lysjk.result.Result;
import com.lysjk.service.ExcelParseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * Excel解析控制器
 * 用于处理Excel文件的上传和解析，将Excel数据录入到数据库中
 */
@RestController
@RequestMapping("/excel")
@Slf4j
public class ExcelController {

    @Autowired
    private ExcelParseService excelParseService;

    /**
     * 解析Excel文件并录入数据库
     *
     * @param file Excel文件
     * @return 解析结果
     */
    @PostMapping("/parse")
    public Result<String> parseExcel(@RequestParam("file") MultipartFile file) {

        log.info("开始解析Excel文件: {}, 大小: {} bytes",
            file.getOriginalFilename(), file.getSize());

        try {
            // 文件基本验证
            if (file.isEmpty()) {
                log.warn("上传的Excel文件为空");
                return Result.error("上传的文件为空");
            }

            // 文件类型验证
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null ||
                (!originalFilename.toLowerCase().endsWith(".xlsx") &&
                 !originalFilename.toLowerCase().endsWith(".xls"))) {
                log.warn("上传的文件不是Excel格式: {}", originalFilename);
                return Result.error("请上传Excel格式文件(.xlsx或.xls)");
            }

            // 文件大小验证（限制为50MB）
            if (file.getSize() > 50 * 1024 * 1024) {
                log.warn("上传的Excel文件过大: {} bytes", file.getSize());
                return Result.error("文件大小不能超过50MB");
            }

            // 调用服务解析Excel文件
            String result = excelParseService.parseAndSaveExcel(file);

            log.info("Excel文件解析完成: {}", result);
            return Result.success(result);

        } catch (Exception e) {
            return Result.error("解析Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取Excel模板文件
     *
     * @return 模板文件下载链接或说明
     */
    @GetMapping("/template")
    public Result<String> getTemplate() {
        log.info("获取Excel模板文件");

        // 这里可以返回模板文件的下载链接或者说明
        String templateInfo = "Excel模板包含以下工作表：\n" +
            "1. 主表 - 地物信息主表\n" +
            "2. 监测点信息表 - 监测点基本信息\n" +
            "3. 环境参数表 - 水域环境参数\n" +
            "4. 水质参数表 - 水质检测参数\n" +
            "5. 反射率光谱表 - 光谱数据\n" +
            "6. 测量记录表 - 测量记录信息\n" +
            "7. 影像信息表 - 影像相关信息\n\n" +
            "请按照模板格式填写数据，确保数据的准确性和完整性。";

        return Result.success(templateInfo);
    }

    /**
     * 获取Excel解析状态和说明
     *
     * @return 解析说明
     */
    @GetMapping("/info")
    public Result<String> getParseInfo() {
        log.info("获取Excel解析说明");

        String info = "Excel解析功能说明：\n" +
            "1. 支持.xlsx和.xls格式的Excel文件\n" +
            "2. 文件大小限制为50MB\n" +
            "3. 解析顺序：地物信息主表 → 监测点信息表 → 其他表格\n" +
            "4. 坐标格式支持：度分秒格式将自动转换为十进制度\n" +
            "5. 日期时间处理：支持多种日期格式，时间默认为00:00\n" +
            "6. 关联字段处理：自动根据编码和名称建立表间关联\n" +
            "7. 事务处理：任一表格解析失败将回滚所有操作\n" +
            "8. 错误处理：详细的错误信息和行号定位";

        return Result.success(info);
    }
}
