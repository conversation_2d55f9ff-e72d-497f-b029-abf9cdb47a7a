package com.lysjk.controller;

import com.lysjk.anno.LogOperation;
import com.lysjk.dto.TWaterSpectrumPageQueryDTO;
import com.lysjk.entity.TWaterSpectrum;
import com.lysjk.result.PageResult;
import com.lysjk.result.Result;
import com.lysjk.service.TWaterSpectrumService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import java.util.List;

/**
 * 波谱信息控制层
 */
@RestController
@RequestMapping("/waterSpectrum")
@Slf4j
public class TWaterSpectrumController {

    @Autowired
    private TWaterSpectrumService waterSpectrumService;

    /**
     * 新增波谱信息
     * @param waterSpectrum 波谱信息
     * @return 操作结果
     */
    @PostMapping
    @LogOperation(operationName = "新增波谱信息")
    public Result save(@RequestBody TWaterSpectrum waterSpectrum) {
        log.info("新增波谱信息: {}", waterSpectrum);
        waterSpectrumService.save(waterSpectrum);
        return Result.success();
    }

    /**
     * 批量删除波谱信息
     * 批量删除格式为?ids=1,2,3
     * @param ids ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @LogOperation(operationName = "删除波谱信息")
    public Result delete(@RequestParam List<Integer> ids) {
        log.info("批量删除波谱信息: {}", ids);
        waterSpectrumService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 更新波谱信息
     * 必传id，然后是json格式
     * @param waterSpectrum 波谱信息
     * @return 操作结果
     */
    @PutMapping
    @LogOperation(operationName = "更新波谱信息")
    public Result update(@RequestBody TWaterSpectrum waterSpectrum) {
        log.info("更新波谱信息: {}", waterSpectrum);
        waterSpectrumService.update(waterSpectrum);
        return Result.success();
    }

    /**
     * 分页查询波谱信息
     * 可以根据多个条件进行分页查询
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @GetMapping("/page")
    @LogOperation(operationName = "分页查询波谱信息")
    public Result<PageResult> selectPage(TWaterSpectrumPageQueryDTO pageQueryDTO) {
        log.info("分页查询波谱信息: {}", pageQueryDTO);
        PageResult pageResult = waterSpectrumService.selectPage(pageQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 从CSV文件导入波谱数据
     * CSV格式：第一列为波长，后续列为不同样本的波谱数值
     * @param file CSV文件
     * @return 操作结果
     */
    @PostMapping("/importCsv")
    @LogOperation(operationName = "导入CSV波谱数据")
    public Result importSpectrumFromCsv(@RequestParam("file") MultipartFile file) {
        log.info("导入CSV波谱数据，文件名: {}", file.getOriginalFilename());
        waterSpectrumService.importSpectrumFromCsv(file);
        return Result.success();
    }

}