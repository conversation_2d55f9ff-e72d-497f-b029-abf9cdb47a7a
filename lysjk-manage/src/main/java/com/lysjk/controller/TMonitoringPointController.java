package com.lysjk.controller;

import com.lysjk.anno.LogOperation;
import com.lysjk.dto.TMonitoringPointPageQueryDTO;
import com.lysjk.entity.TMonitoringPoint;
import com.lysjk.exception.ValidationException;
import com.lysjk.result.PageResult;
import com.lysjk.result.Result;
import com.lysjk.service.TMonitoringPointService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 监测点信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/monitoringPoint")
public class TMonitoringPointController {

    @Autowired
    private TMonitoringPointService tMonitoringPointService;

    /**
     * 新增监测点信息
     * @param monitoringPoint 监测点信息
     * @return 操作结果
     */
    @PostMapping
    @LogOperation(operationName = "新增监测点信息")
    public Result save(@RequestBody TMonitoringPoint monitoringPoint) {
        log.info("新增监测点信息: {}", monitoringPoint);
        tMonitoringPointService.save(monitoringPoint);
        return Result.success();
    }

    /**
     * 批量删除监测点信息
     * 批量删除格式为?ids=1,2,3
     * @param ids ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @LogOperation(operationName = "删除监测点信息")
    public Result delete(@RequestParam List<Integer> ids) {
        log.info("批量删除监测点信息: {}", ids);
        tMonitoringPointService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 更新监测点信息
     * 必传id,然后是json格式
     * @param monitoringPoint 监测点信息
     * @return 操作结果
     */
    @PutMapping
    @LogOperation(operationName = "更新监测点信息")
    public Result update(@RequestBody TMonitoringPoint monitoringPoint) {
        log.info("更新监测点信息: {}", monitoringPoint);
        tMonitoringPointService.update(monitoringPoint);
        return Result.success();
    }

    /**
     * 根据ID查询监测点信息
     */
    @LogOperation(operationName = "查询监测点信息")
    @GetMapping("/{id}")
    public Result<TMonitoringPoint> selectById(@PathVariable Integer id) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("监测点ID");
        }
        TMonitoringPoint monitoringPoint = tMonitoringPointService.selectByPrimaryKey(id);
        if (monitoringPoint != null) {
            return Result.success(monitoringPoint);
        } else {
            throw new ValidationException.DataNotFoundException("监测点", id.toString());
        }
    }

    /**
     * 分页条件查询监测点信息
     * 可以根据多个条件进行分页查询
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @GetMapping("/page")
    @LogOperation(operationName = "分页查询监测点信息")
    public Result<PageResult> selectPage(TMonitoringPointPageQueryDTO pageQueryDTO) {
        log.info("分页查询监测点信息: {}", pageQueryDTO);
        PageResult pageResult = tMonitoringPointService.selectPage(pageQueryDTO);
        return Result.success(pageResult);
    }
}
