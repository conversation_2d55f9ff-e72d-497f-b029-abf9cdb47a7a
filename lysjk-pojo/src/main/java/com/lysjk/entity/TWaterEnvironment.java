package com.lysjk.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lysjk.config.serializer.FlexibleLocalDateTimeDeserializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 水域环境信息表
 * t_water_environment
 * 目前25个字段
 */
@Data
public class TWaterEnvironment implements Serializable {
    /**
     * 唯一ID
     */
    private Integer id;

    /**
     * 监测点ID
     */
    private Integer pointId;

    /**
     * 所属地物ID
     */
    private Integer regionId;

    /**
     * 监测时间(这个我没有用aop主动拦截,所以需要手动处理一下)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8") // 查询时返回给前端的格式,数据库存储的还是带秒HH:mm:00
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime monitoringDt;

    /**
     * 风速
     */
    private Double windSpeed;

    /**
     * 风向
     */
    private String windDirection;

    /**
     * 气温
     */
    private Double airTemperature;

    /**
     * 水温
     */
    private Double waterTemperature;

    /**
     * 水体透明度
     */
    private Double waterTransparency;

    /**
     * 拓展|测量工具和人员
     * json格式,直接存储JSON字符串
     * 数据库传递的形式类似{"王超": "仪器1", "杨旭": "集思宝A8", "靳兴浩":"赛氏盘","胡军周":"仪器填写完整"}
     */
    private String extension;

    /**
     * 水深度
     */
    private Double waterDepth;

    /**
     * 天气
     */
    private String weather;

    /**
     * 水面状况
     */
    private String surfaceCondition;

    /**
     * 照片文件夹(序号)
     * 格式暂定
     */
    private String photoFolder;

    /**
     * 水体浊度平均值
     */
    private Double waterTurbidity;

    /**
     * 溶解氧
     */
    private Double dissolvedOxyg;

    /**
     * 水体浊度3个记录
     * 先写成这种格式吧
     * 1.86\1.59\1.65
     * 1.87\2.00\1.93
     */
    private String waterTurbidity3;

    /**
     * 海拔
     */
    private Double elevation;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录修改者ID
     */
    private Integer updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 新添加字段,我认为ph肯定是填写小数的,所以NA这种暂时不写
     */
    private Double ph;

    /**
     * 新增字段反射率光谱
     * 0-79（3组）
     */
    private String reflectanceSpectrum;

    private static final long serialVersionUID = 1L;
}