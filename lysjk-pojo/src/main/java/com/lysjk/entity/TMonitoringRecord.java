package com.lysjk.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 测量记录表
 * t_monitoring_record
 * 目前16个字段
 */
@Data
public class TMonitoringRecord implements Serializable {
    /**
     * 主键ID，自增
     */
    private Integer id;

    /**
     * 点位测量记录
     */
    private String pointMeasure;

    /**
     * 风向风速
     */
    private String windDirectionSpeed;

    /**
     * 光谱测量
     */
    private String spectralMeasure;

    /**
     * 透明度测量
     */
    private String transparencyMeasure;

    /**
     * 浊度测量
     */
    private String turbidityMeasure;

    /**
     * 深度测量
     */
    private String depthMeasure;

    /**
     * 水色照片
     */
    private String waterColorPhoto;

    /**
     * 实验室检测
     */
    private String laboratoryTest;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录修改者ID
     */
    private Integer updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 监测点Id
     */
    private Integer pointId;

    /**
     * 地物Id
     */
    private Integer regionId;

    private static final long serialVersionUID = 1L;
}