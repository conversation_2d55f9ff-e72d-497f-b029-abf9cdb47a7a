package com.lysjk.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lysjk.config.serializer.*;
import lombok.Data;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Point;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 地物信息主表
 * t_region_info
 * 目前22个字段
 */
@Data
public class TRegionInfo implements Serializable {
    /**
     * 地物ID
     */
    private Integer id;

    /**
     * 地物编码(市加区县组合)
     */
    private String code;

    /**
     * 地物名称
     */
    private String name;

    /**
     * 省名称
     */
    private String sheng;

    /**
     * 市名称
     */
    private String shi;

    /**
     * 区县名称
     */
    private String qu;

    /**
     * 街镇名称
     */
    private String zhen;

    /**
     * 坐落位置
     */
    private String zuoluo;

    /**
     * 中心点坐标 - PostGIS POINT类型
     */
    @JsonSerialize(using = PointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    private Point center;

    /**
     * 范围坐标串 - PostGIS MULTIPOLYGON类型
     */
    @JsonSerialize(using = MultiPolygonSerializer.class)
    @JsonDeserialize(using = MultiPolygonDeserializer.class)
    private MultiPolygon region;

    /**
     * 备注
     */
    private String remark;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建人ID
     */
    private Integer createBy;

    /**
     * 记录更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录更新人ID
     */
    private Integer updateBy;

    /**************************补充以下字段*****************************/

    /**
     * 是否包含 1:包含 0:未包含
     */
    private Integer status;

    /**
     * 所属河系
     */
    private String river;

    /**
     * 传递给前端的格式就直接JsonFormat控制即可(而且此时一般是固定的)
     * 采样时间 年月日即可
     * 加上这个就可以正常处理了
     * 而且查询返回的json格式使用的是entity中的完整信息,如果是vo的话需要JsonFormat,dto就不需要了
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8") // 主要传递给前端的Json格式,但也间接支持反序列化
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class) // 处理非表单提供的Json数据(比如直接的json),反序列化
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm") // 处理前端表单提供的Json数据,如果没写DTO则需要在这里写
    private LocalDateTime sampleDt; // 因为LocalDateTime默认需要日期加时间,所以JsonDeserialize主要就是解决该问题,自定义拼接好了
    // 如果用LocalDate则又和数据库的timestamp起冲突了

    /**
     * 编号
     */
    private String number;

    /**
     * 采集方法
     */
    private String method;

    /**
     * 完成单位
     */
    private String unit;

    /**
     * 行政编码
     */
    private String administrativeCode;

    private static final long serialVersionUID = 1L;
}