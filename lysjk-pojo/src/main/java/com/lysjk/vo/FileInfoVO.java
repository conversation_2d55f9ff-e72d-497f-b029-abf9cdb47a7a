package com.lysjk.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文件信息VO
 */
@Data
public class FileInfoVO implements Serializable {
    // 文件名
    private String fileName;
    // 原始文件名
    private String originalFileName;
    // 文件大小(字节)
    private Long fileSize;
    // 文件类型
    private String contentType;
    // 文件扩展名
    private String extension;
    // 上传时间
    private LocalDateTime uploadTime;
    // 文件描述
    private String description;
    // 文件分类
    private String category;
    // 下载URL
    private String downloadUrl;

    private static final long serialVersionUID = 1L;
}
