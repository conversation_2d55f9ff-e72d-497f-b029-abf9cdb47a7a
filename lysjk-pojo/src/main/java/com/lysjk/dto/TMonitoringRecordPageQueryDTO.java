package com.lysjk.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 测量记录分页查询DTO
 */
@Data
public class TMonitoringRecordPageQueryDTO implements Serializable {

    //页码(先默认设值)
    private int page = 1;

    //每页显示记录数
    private int pageSize = 10;

    /**
     * 监测点ID
     */
    private Integer pointId;

    /**
     * 点位测量记录
     */
    private String pointMeasure;

    /**
     * 风向风速
     */
    private String windDirectionSpeed;

    /**
     * 光谱测量
     */
    private String spectralMeasure;

    /**
     * 透明度测量
     */
    private String transparencyMeasure;

    /**
     * 浊度测量
     */
    private String turbidityMeasure;

    /**
     * 深度测量
     */
    private String depthMeasure;

    /**
     * 水色照片
     */
    private String waterColorPhoto;

    /**
     * 实验室检测
     */
    private String laboratoryTest;

    /**
     * 备注
     */
    private String remark;

    /**
     * 地物Id
     */
    private Integer regionId;

    private static final long serialVersionUID = 1L;
}
