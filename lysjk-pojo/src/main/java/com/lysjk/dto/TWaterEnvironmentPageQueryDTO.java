package com.lysjk.dto;


import lombok.Data;

import java.io.Serializable;

/**
 * 主要是封装分页条件查询的数据
 */
@Data
public class TWaterEnvironmentPageQueryDTO implements Serializable {

    //页码(先默认设值)
    private int page = 1;

    //每页显示记录数
    private int pageSize = 10;

    /**
     * 监测点ID
     */
    private Integer pointId;

    /**
     * 所属地物ID
     */
    private Integer regionId;

    /**
     * 风速
     */
    private Double windSpeed;

    /**
     * 风向
     */
    private String windDirection;

    /**
     * 气温
     */
    private Double airTemperature;

    /**
     * 水温
     */
    private Double waterTemperature;

    /**
     * 水体透明度
     */
    private Double waterTransparency;

    /**
     * 拓展|测量工具和人员
     * json格式,直接存储JSON字符串
     * 数据库传递的形式类似{"王超": "仪器1", "杨旭": "集思宝A8", "靳兴浩":"赛氏盘","胡军周":"仪器填写完整"}
     */
    private String extension;

    /**
     * 水深度
     */
    private Double waterDepth;

    /**
     * 天气
     */
    private String weather;

    /**
     * 水面状况
     */
    private String surfaceCondition;

    /**
     * 照片文件夹
     * 这个目前可以为null
     */
    private String photoFolder;

    /**
     * 水体浊度平均值
     */
    private Double waterTurbidity;

    /**
     * 溶解氧
     */
    private Double dissolvedOxyg;

    /**
     * 水体浊度3个记录
     * 先写成这种格式吧
     * 1.86\1.59\1.65
     * 1.87\2.00\1.93
     */
    private String waterTurbidity3;

    /**
     * 海拔
     */
    private Double elevation;

    /**
     * ph
     */
    private Double ph;

    /**
     * 反射率光谱格式比如
     * 0-79（3组）
     */
    private String reflectanceSpectrum;

    private static final long serialVersionUID = 1L;
}
