package com.lysjk.dto.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lysjk.config.serializer.PointDeserializer;
import com.lysjk.config.serializer.PointSerializer;
import lombok.Data;
import org.locationtech.jts.geom.Point;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 监测点信息表
 * 对应t_monitoring_point
 */
@Data
public class TMonitoringPointExcelDTO implements Serializable {
    /**
     * 监测点信息表ID主键自增
     * 这个就是其他表要关联的pointId
     */
    private Integer id;

    /**
     * 采样点号
     * 注意该字段是直接存储到该数据库中
     */
    private String code;

    /**
     * 地点
     */
    private String name;

    /**
     * 所属地物ID 注意此时是根据name来查询地物信息表id并给这个regionId赋值
     */
    private Integer regionId;

    /**
     * 地理坐标 - PostGIS POINT类型
     * 注意读取时,经度和纬度这俩个字段共同对应location,需要先进行度分秒转换10进制以及拼接,然后存储在我数据库中,POINT(112.095741 34.115383) | 4326
     */
    @JsonSerialize(using = PointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    private Point location;

    private static final long serialVersionUID = 1L;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录修改者ID
     */
    private Integer updateBy;
}