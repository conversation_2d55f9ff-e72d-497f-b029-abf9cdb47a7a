package com.lysjk.dto.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * excel反射率光谱表
 * 对应t_water_spectrum
 */
@Data
public class TWaterSpectrumExcelDTO implements Serializable {
    /**
     * 波普信息表ID主键自增
     */
    private Integer id;

    /**
     * 采样点号
     * 注意该字段是excel中写得(数据库并没有存储),用来填写关联的pointId和regionId
     */
    private String code;

    /**
     * 监测点ID 通过code关联查询监测点表
     */
    private Integer pointId;

    /**
     * 所属地物ID 通过code关联查询监测点表
     */
    private Integer regionId;


    /**
     * 波谱数据
     * 这些可以参考我的TWaterSpectrumController中的importSpectrumFromCsv,不过此时应该是读取xlsx,但是存储格式仍然是{"400":0.005529024,"401":0.005508018,"402":0.005485608,"403":0.005472551,"404":0.005469064}对应数据库的spectrum字段
     */
    private Map<String, Double> spectrum;

    private static final long serialVersionUID = 1L;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录修改者ID
     */
    private Integer updateBy;
}