package com.lysjk.dto.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lysjk.config.serializer.FlexibleLocalDateTimeDeserializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * excel主表
 * 对应t_region_info
 */
@Data
public class TRegionInfoMainExcelDTO implements Serializable {
    /**
     * 地物信息表ID主键自增
     */
    private Integer id;

    /**
     * 序号
     */
    private String number;

    /**
     * 地物编码(市加区县组合)
     */
    private String code;

    /**
     * 采集时间 注意拼接00:00
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime sampleDt;

    /**
     * 采集地点
     */
    private String name;

    /**
     * 采集方法
     */
    private String method;

    /**
     * 完成单位
     */
    private String unit;

    /**
     * 备注
     */
    private String remark;

    private static final long serialVersionUID = 1L;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录修改者ID
     */
    private Integer updateBy;
}
