package com.lysjk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lysjk.config.serializer.FlexibleLocalDateTimeDeserializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作日志分页查询DTO
 */
@Data
public class OperateLogPageQueryDTO implements Serializable {

    //页码(先默认设值)
    private int page = 1;

    //每页显示记录数
    private int pageSize = 10;

    /**
     * 操作账号
     */
    private String username;

    /**
     * 操作名称（如"用户登录"）
     */
    private String operationName;

    /**
     * 操作类型（如"SELECT"、"INSERT"）
     */
    private String operationType;

    /**
     * 操作的请求路径url
     */
    private String operationUrl;

    /**
     * 操作人ID
     */
    private Integer userId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    private static final long serialVersionUID = 1L;
}
