# Excel解析功能说明

## 功能概述

本系统提供了完整的Excel文件解析功能，可以将Excel中的水质监测数据批量导入到数据库中。支持多个工作表的数据解析，包括地物信息、监测点信息、环境参数、水质参数、光谱数据等。

## 支持的Excel格式

- **文件格式**: .xlsx 和 .xls
- **文件大小**: 最大50MB
- **编码格式**: UTF-8

## Excel工作表结构

### 1. 主表（地物信息主表）
- **工作表名称**: "主表"
- **对应数据表**: t_region_info
- **字段说明**:
  - 序号: 地物编号
  - 地物编码: 唯一标识码
  - 采集时间: 日期格式，时间自动设置为00:00
  - 采集地点: 地物名称
  - 采集方法: 采集方式说明
  - 完成单位: 负责单位
  - 备注: 附加说明

### 2. 监测点信息表
- **工作表名称**: "监测点信息表"
- **对应数据表**: t_monitoring_point
- **字段说明**:
  - 采样点号: 监测点编码
  - 地点: 监测点名称（用于关联地物信息）
  - 经度: 支持度分秒格式，如"112°05'44.67\""
  - 纬度: 支持度分秒格式，如"34°06'55.38\""

### 3. 环境参数表
- **工作表名称**: "环境参数表"
- **对应数据表**: t_water_environment
- **字段说明**:
  - 采样点号: 关联监测点
  - 监测时间: 日期和时间需要分别填写
  - 风速、风向、海拔、气温、水温等环境参数

### 4. 水质参数表
- **工作表名称**: "水质参数表"
- **对应数据表**: t_water_quality
- **字段说明**:
  - 采样点号: 关联监测点
  - 透明度、浊度、总悬浮物等水质指标

### 5. 反射率光谱表
- **工作表名称**: "反射率光谱表"
- **对应数据表**: t_water_spectrum
- **字段说明**:
  - 采样点号: 关联监测点
  - 波长数据: 从第2列开始，每列代表一个波长的反射率值

### 6. 测量记录表
- **工作表名称**: "测量记录表"
- **对应数据表**: t_monitoring_record
- **字段说明**:
  - 采样点号: 关联监测点
  - 各种测量记录和操作说明

### 7. 影像信息表
- **工作表名称**: "影像信息表"
- **对应数据表**: t_image
- **字段说明**:
  - 过境时间: 影像获取时间
  - 卫星传感器: 传感器类型

## 数据处理规则

### 坐标转换
- **支持格式**:
  - 度分秒格式: "112°05'44.67\""、"112度05分44.67秒"
  - 空格分隔: "112 05 44.67"
  - 度分格式: "112°05'"
  - 十进制度: "112.095741"
- **输出格式**: PostGIS POINT类型，SRID=4326

### 日期时间处理
- **日期格式**: 支持多种格式，如"yyyy-MM-dd"、"yyyy/MM/dd"等
- **时间处理**: 
  - 地物信息表: 日期+00:00
  - 环境参数表: 日期+时间拼接
  - 其他表: 日期+00:00

### 关联字段处理
- **地物关联**: 监测点通过"地点"字段关联地物信息表的"name"字段
- **监测点关联**: 其他表通过"采样点号"关联监测点表的"code"字段

## API接口

### 1. 解析Excel文件
```
POST /excel/parse
Content-Type: multipart/form-data
参数: file (Excel文件)
```

### 2. 获取模板说明
```
GET /excel/template
```

### 3. 获取解析说明
```
GET /excel/info
```

## 使用步骤

1. **准备Excel文件**
   - 按照工作表结构准备数据
   - 确保坐标格式正确
   - 检查关联字段的一致性

2. **上传文件**
   - 调用 `/excel/parse` 接口
   - 上传Excel文件

3. **查看结果**
   - 系统返回解析结果和录入统计
   - 如有错误，会返回详细的错误信息

## 注意事项

### 数据录入顺序
1. 地物信息主表（必须先录入）
2. 监测点信息表（依赖地物信息）
3. 其他表格（依赖监测点信息）

### 事务处理
- 所有表格作为一个事务处理
- 任一表格解析失败，整个操作回滚
- 确保数据的一致性

### 错误处理
- 详细的错误信息和行号定位
- 坐标格式错误会中断处理
- 关联字段不存在会记录警告但继续处理

### 性能考虑
- 大文件建议分批处理
- 复杂光谱数据可能需要较长处理时间
- 建议在低峰期进行批量导入

## 常见问题

### Q: 坐标格式不正确怎么办？
A: 系统支持多种度分秒格式，请参考"坐标转换"部分的支持格式。

### Q: 关联字段找不到怎么办？
A: 确保监测点的"地点"字段与地物信息表的"name"字段完全一致。

### Q: 光谱数据如何填写？
A: 从第2列开始，每列代表一个波长的反射率值，系统会自动从400nm开始递增。

### Q: 日期时间格式要求？
A: 支持多种日期格式，时间格式为"HH:mm"，具体请参考"日期时间处理"部分。

## 技术实现

- **框架**: Spring Boot + MyBatis
- **Excel解析**: Apache POI
- **坐标处理**: JTS (Java Topology Suite)
- **数据库**: PostgreSQL + PostGIS
- **事务管理**: Spring Transaction

## 更新日志

- **v1.0**: 初始版本，支持基本的Excel解析功能
- 支持7个工作表的数据解析
- 实现坐标转换和关联字段处理
- 添加完整的错误处理和事务管理
