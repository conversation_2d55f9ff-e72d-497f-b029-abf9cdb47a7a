package com.lysjk.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 坐标转换工具类
 * 用于处理度分秒格式与十进制度格式之间的转换
 * 支持多种度分秒格式的解析
 */
@Slf4j
public class CoordinateUtil {

    // 度分秒格式的正则表达式模式
    // 支持格式：112°05'44.67"、112度05分44.67秒、112°05′44.67″、112 05 44.67等
    private static final Pattern DMS_PATTERN_1 = Pattern.compile("(\\d+)[°度]\\s*(\\d+)[′'分]\\s*(\\d+(?:\\.\\d+)?)[″\"秒]?");
    private static final Pattern DMS_PATTERN_2 = Pattern.compile("(\\d+)\\s+(\\d+)\\s+(\\d+(?:\\.\\d+)?)");
    private static final Pattern DMS_PATTERN_3 = Pattern.compile("(\\d+)[°度]\\s*(\\d+(?:\\.\\d+)?)[′'分]?");
    private static final Pattern DMS_PATTERN_4 = Pattern.compile("(\\d+(?:\\.\\d+)?)");

    /**
     * 将度分秒格式转换为十进制度
     * 
     * @param dmsStr 度分秒格式字符串，如："112°05'44.67""、"112度05分44.67秒"、"112 05 44.67"等
     * @return 十进制度数值
     * @throws IllegalArgumentException 如果输入格式无效
     */
    public static double dmsToDecimal(String dmsStr) {
        if (dmsStr == null || dmsStr.trim().isEmpty()) {
            throw new IllegalArgumentException("度分秒字符串不能为空");
        }

        String trimmed = dmsStr.trim();
        log.debug("解析度分秒字符串: {}", trimmed);

        // 尝试各种格式的解析
        Matcher matcher;

        // 格式1: 112°05'44.67" 或 112度05分44.67秒
        matcher = DMS_PATTERN_1.matcher(trimmed);
        if (matcher.matches()) {
            int degrees = Integer.parseInt(matcher.group(1));
            int minutes = Integer.parseInt(matcher.group(2));
            double seconds = Double.parseDouble(matcher.group(3));
            double result = degrees + minutes / 60.0 + seconds / 3600.0;
            log.debug("解析成功 (格式1): {}°{}'{}\", 结果: {}", degrees, minutes, seconds, result);
            return result;
        }

        // 格式2: 112 05 44.67 (空格分隔)
        matcher = DMS_PATTERN_2.matcher(trimmed);
        if (matcher.matches()) {
            int degrees = Integer.parseInt(matcher.group(1));
            int minutes = Integer.parseInt(matcher.group(2));
            double seconds = Double.parseDouble(matcher.group(3));
            double result = degrees + minutes / 60.0 + seconds / 3600.0;
            log.debug("解析成功 (格式2): {} {} {}, 结果: {}", degrees, minutes, seconds, result);
            return result;
        }

        // 格式3: 112°05' 或 112度05分 (只有度分)
        matcher = DMS_PATTERN_3.matcher(trimmed);
        if (matcher.matches()) {
            int degrees = Integer.parseInt(matcher.group(1));
            double minutes = Double.parseDouble(matcher.group(2));
            double result = degrees + minutes / 60.0;
            log.debug("解析成功 (格式3): {}°{}', 结果: {}", degrees, minutes, result);
            return result;
        }

        // 格式4: 112.095741 (已经是十进制度)
        matcher = DMS_PATTERN_4.matcher(trimmed);
        if (matcher.matches()) {
            double result = Double.parseDouble(matcher.group(1));
            log.debug("解析成功 (格式4): 已是十进制度: {}", result);
            return result;
        }

        throw new IllegalArgumentException("无法解析度分秒格式: " + dmsStr + 
            "，支持的格式：112°05'44.67\"、112度05分44.67秒、112 05 44.67、112°05'、112.095741");
    }

    /**
     * 将十进制度转换为度分秒格式
     * 
     * @param decimal 十进制度数值
     * @return 度分秒格式字符串，如："112°05'44.67\""
     */
    public static String decimalToDms(double decimal) {
        if (decimal < -180 || decimal > 180) {
            throw new IllegalArgumentException("经纬度值必须在-180到180之间: " + decimal);
        }

        boolean negative = decimal < 0;
        decimal = Math.abs(decimal);

        int degrees = (int) decimal;
        double minutesDecimal = (decimal - degrees) * 60;
        int minutes = (int) minutesDecimal;
        double seconds = (minutesDecimal - minutes) * 60;

        String result = String.format("%s%d°%02d'%06.3f\"", 
            negative ? "-" : "", degrees, minutes, seconds);
        
        log.debug("十进制度转换为度分秒: {} -> {}", (negative ? -1 : 1) * decimal, result);
        return result;
    }

    /**
     * 验证经度值是否有效
     * 
     * @param longitude 经度值
     * @return 是否有效
     */
    public static boolean isValidLongitude(double longitude) {
        return longitude >= -180.0 && longitude <= 180.0;
    }

    /**
     * 验证纬度值是否有效
     * 
     * @param latitude 纬度值
     * @return 是否有效
     */
    public static boolean isValidLatitude(double latitude) {
        return latitude >= -90.0 && latitude <= 90.0;
    }

    /**
     * 解析经纬度字符串并验证有效性
     * 
     * @param longitudeStr 经度字符串
     * @param latitudeStr 纬度字符串
     * @return 包含经度和纬度的数组 [longitude, latitude]
     * @throws IllegalArgumentException 如果坐标无效
     */
    public static double[] parseAndValidateCoordinates(String longitudeStr, String latitudeStr) {
        if (longitudeStr == null || longitudeStr.trim().isEmpty()) {
            throw new IllegalArgumentException("经度字符串不能为空");
        }
        if (latitudeStr == null || latitudeStr.trim().isEmpty()) {
            throw new IllegalArgumentException("纬度字符串不能为空");
        }

        try {
            double longitude = dmsToDecimal(longitudeStr);
            double latitude = dmsToDecimal(latitudeStr);

            if (!isValidLongitude(longitude)) {
                throw new IllegalArgumentException("经度值无效，必须在-180到180之间: " + longitude);
            }
            if (!isValidLatitude(latitude)) {
                throw new IllegalArgumentException("纬度值无效，必须在-90到90之间: " + latitude);
            }

            log.debug("坐标解析和验证成功: 经度={}, 纬度={}", longitude, latitude);
            return new double[]{longitude, latitude};
        } catch (Exception e) {
            log.error("坐标解析失败: 经度={}, 纬度={}", longitudeStr, latitudeStr, e);
            throw new IllegalArgumentException("坐标解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 格式化坐标为标准显示格式
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @return 格式化的坐标字符串，如："112.095741, 34.115383"
     */
    public static String formatCoordinates(double longitude, double latitude) {
        return String.format("%.6f, %.6f", longitude, latitude);
    }

    /**
     * 创建WKT格式的POINT字符串
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @return WKT格式字符串，如："POINT(112.095741 34.115383)"
     */
    public static String createWktPoint(double longitude, double latitude) {
        return String.format("POINT(%.6f %.6f)", longitude, latitude);
    }
}
