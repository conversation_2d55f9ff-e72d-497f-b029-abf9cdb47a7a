package com.lysjk.config.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.lysjk.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 灵活的LocalDateTime反序列化器
 * 支持多种日期时间格式的自动识别和转换
 * 
 * <AUTHOR>
 */
@Slf4j
public class FlexibleLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

    @Override
    public LocalDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        String dateTimeStr = parser.getText();
        
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 使用DateTimeUtil的智能解析功能
            LocalDateTime result = DateTimeUtil.parseDateTime(dateTimeStr.trim());
            
            if (result != null) {
                log.debug("成功反序列化日期时间: {} -> {}", dateTimeStr, result);
                return result;
            } else {
                log.warn("无法解析日期时间字符串: {}", dateTimeStr);
                // 如果解析失败，返回null而不是抛出异常，让业务层处理
                return null;
            }
        } catch (Exception e) {
            log.error("反序列化日期时间失败: {}", dateTimeStr, e);
            // 返回null而不是抛出异常，保持系统稳定性
            return null;
        }
    }
}